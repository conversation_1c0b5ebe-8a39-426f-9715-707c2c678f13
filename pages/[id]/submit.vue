<template>
  <div>
    <Head>
      <Title>{{ $t('feedback.title') }} - {{ userDisplayName }} | voice.new</Title>
      <Meta name="description" :content="$t('feedback.description')" />
      <Meta name="keywords" content="submit feedback, user feedback, suggestions, voice.new" />
      <Link rel="canonical" :href="`${$config.public.baseUrl}${$route.path}`" />
    </Head>

    <div class="container-narrow">
      <!-- Back Button -->
      <div class="mb-6">
        <NuxtLink :to="`/${userId}`" class="btn btn-ghost">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          {{ $t('common.back') }}
        </NuxtLink>
      </div>

      <!-- Header -->
      <div class="text-center mb-8">
        <div class="w-12 h-12 blue-accent flex items-center justify-center mx-auto mb-4">
          <span class="text-lg font-bold text-white">{{ userInitial }}</span>
        </div>
        <h1 class="text-3xl md:text-4xl font-bold text-primary mb-4">
          {{ $t('feedback.titleFor', { name: userDisplayName }) }}
        </h1>
        <p class="text-lg text-secondary">
          {{ $t('feedback.description') }}
        </p>
      </div>

      <!-- Form -->
      <div class="card">
        <form @submit.prevent="submitFeedback" class="space-y-6">
          <!-- Title Field -->
          <div>
            <label for="title" class="block text-sm font-medium text-primary mb-2">
              {{ $t('feedback.form.title') }}
            </label>
            <input
              id="title"
              v-model="form.title"
              type="text"
              class="input"
              :placeholder="$t('feedback.form.titlePlaceholder')"
              required
            />
          </div>

          <!-- Description Field -->
          <div>
            <label for="description" class="block text-sm font-medium text-primary mb-2">
              {{ $t('feedback.form.description') }}
            </label>
            <textarea
              id="description"
              v-model="form.description"
              rows="4"
              class="input"
              :placeholder="$t('feedback.form.descriptionPlaceholder')"
              required
            ></textarea>
          </div>

          <!-- Category Field -->
          <div>
            <label for="category" class="block text-sm font-medium text-primary mb-2">
              {{ $t('feedback.form.category') }}
            </label>
            <select id="category" v-model="form.category" class="input" required>
              <option value="">{{ $t('feedback.form.selectCategory') }}</option>
              <option value="feature">{{ $t('feedback.categories.feature') }}</option>
              <option value="bug">{{ $t('feedback.categories.bug') }}</option>
              <option value="improvement">{{ $t('feedback.categories.improvement') }}</option>
              <option value="other">{{ $t('feedback.categories.other') }}</option>
            </select>
          </div>

          <!-- Contact Info (Optional) -->
          <div>
            <label for="contact" class="block text-sm font-medium text-primary mb-2">
              {{ $t('feedback.form.contact') }}
              <span class="text-tertiary text-xs ml-1">({{ $t('common.optional') }})</span>
            </label>
            <input
              id="contact"
              v-model="form.contact"
              type="email"
              class="input"
              :placeholder="$t('feedback.form.contactPlaceholder')"
            />
          </div>

          <!-- Error Message -->
          <div v-if="error" class="text-error text-sm">
            {{ error }}
          </div>

          <!-- Success Message -->
          <div v-if="success" class="text-success text-sm">
            {{ $t('feedback.form.success') }}
          </div>

          <!-- Submit Button -->
          <div class="flex gap-4">
            <button
              type="submit"
              class="btn btn-primary flex-1"
              :disabled="isSubmitting"
            >
              {{ isSubmitting ? $t('common.submitting') : $t('feedback.form.submit') }}
            </button>
            <NuxtLink :to="`/${userId}`" class="btn btn-secondary">
              {{ $t('common.cancel') }}
            </NuxtLink>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'

const route = useRoute()
const router = useRouter()
const config = useRuntimeConfig()
const { submitFeedback: submitFeedbackAPI, getUserInfo } = useFeedback()

// Get user ID from route
const userId = computed(() => route.params.id)

// Reactive data
const userInfo = ref(null)
const isSubmitting = ref(false)
const error = ref('')
const success = ref(false)

const form = ref({
  title: '',
  description: '',
  category: '',
  contact: ''
})

// Computed properties
const userDisplayName = computed(() => {
  return userInfo.value?.name || userId.value || 'User'
})

const userInitial = computed(() => {
  return userDisplayName.value.charAt(0).toUpperCase()
})

// Methods
const loadUserInfo = async () => {
  try {
    const result = await getUserInfo(userId.value)
    if (result.success) {
      userInfo.value = result.data
    }
  } catch (err) {
    console.error('Error loading user info:', err)
  }
}

const submitFeedback = async () => {
  if (isSubmitting.value) return

  isSubmitting.value = true
  error.value = ''
  success.value = false

  try {
    const result = await submitFeedbackAPI({
      ...form.value,
      userId: userId.value
    })

    if (result.success) {
      success.value = true
      // Reset form
      form.value = {
        title: '',
        description: '',
        category: '',
        contact: ''
      }
      
      // Redirect to user page after a short delay
      setTimeout(() => {
        router.push(`/${userId.value}`)
      }, 2000)
    } else {
      error.value = result.error || 'Failed to submit feedback'
    }
  } catch (err) {
    console.error('Error submitting feedback:', err)
    error.value = 'Failed to submit feedback'
  } finally {
    isSubmitting.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadUserInfo()
})
</script>
