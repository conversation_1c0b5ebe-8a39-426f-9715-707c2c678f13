<template>
  <div>
    <!-- <PERSON><PERSON> Head -->
    <Head>
      <Title>{{ feedback?.title || 'Feedback' }} | voice.new</Title>
      <Meta name="description" :content="feedback?.content || 'View feedback details'" />
      <Meta name="keywords" content="feedback, user feedback, details, voice.new" />
      <Link rel="canonical" :href="`${$config.public.baseUrl}${$route.path}`" />
    </Head>

    <div v-if="isLoading" class="text-center py-16">
      <div class="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
      <p class="text-secondary">{{ $t('common.loading') }}</p>
    </div>

    <div v-else-if="error" class="text-center py-16">
      <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <svg class="w-12 h-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 class="text-xl font-semibold text-primary mb-2">
        {{ $t('common.error') }}
      </h3>
      <p class="text-secondary mb-6">
        Feedback not found
      </p>
      <NuxtLink to="/" class="btn btn-primary">
        {{ $t('common.back') }}
      </NuxtLink>
    </div>

    <div v-else-if="feedback" class="container-narrow">
      <!-- Back Button -->
      <div class="mb-6">
        <NuxtLink to="/" class="btn btn-ghost">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          {{ $t('common.back') }}
        </NuxtLink>
      </div>

      <!-- Main Content Grid -->
      <div class="grid lg:grid-cols-4 gap-8">
        <!-- Main Content -->
        <div class="lg:col-span-3">
          <!-- Header with Vote Button -->
          <div class="flex gap-6 mb-6">
            <!-- Vote Button -->
            <div class="flex-shrink-0">
              <VoteButton
                :votes="feedback.votes"
                :voted="feedback.userVoted"
                :feedback-id="feedback.id"
                @vote="handleVote"
              />
            </div>

            <!-- Title and Meta -->
            <div class="flex-1 min-w-0">
              <h1 class="text-3xl font-bold text-primary mb-3">
                {{ feedback.title }}
              </h1>

              <!-- Meta Info -->
              <div class="flex items-center text-sm text-tertiary space-x-4 mb-4">
                <span>{{ feedback.author }}</span>
                <span>•</span>
                <time :datetime="feedback.createdAt">
                  {{ formatDate(feedback.createdAt) }}
                </time>
                <span>•</span>
                <CategoryBadge :category="feedback.category" />
              </div>

              <!-- All Tags -->
              <div v-if="feedback.tags && feedback.tags.length > 0" class="flex flex-wrap gap-2">
                <TagBadge
                  v-for="tag in feedback.tags"
                  :key="tag.id"
                  :tag="tag"
                />
              </div>
            </div>
          </div>

          <!-- Content -->
          <div class="card mb-6">
            <div class="prose prose-lg max-w-none">
              <p class="text-secondary leading-relaxed whitespace-pre-wrap">{{ feedback.content }}</p>
            </div>
          </div>

          <!-- Contact Info -->
          <div v-if="feedback.contact" class="card mb-6">
            <h3 class="text-lg font-semibold text-primary mb-3">
              {{ $t('feedback.contactInfo', 'Contact Information') }}
            </h3>
            <a :href="`mailto:${feedback.contact}`" class="text-primary hover:underline">
              {{ feedback.contact }}
            </a>
          </div>
        </div>

        <!-- Sidebar -->
        <div class="lg:col-span-1">
          <!-- Status & Priority -->
          <div class="card mb-6">
            <h3 class="text-lg font-semibold text-primary mb-4">
              {{ $t('feedback.statusInfo', 'Status & Priority') }}
            </h3>
            <div class="space-y-3">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-secondary">{{ $t('feedback.status.title', 'Status') }}:</span>
                <StatusBadge :status="feedback.status" />
              </div>
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium text-secondary">{{ $t('feedback.priority.title', 'Priority') }}:</span>
                <PriorityBadge :priority="feedback.priority" />
              </div>
            </div>
          </div>

          <!-- Feedback Stats -->
          <div class="card">
            <h3 class="text-lg font-semibold text-primary mb-4">
              {{ $t('feedback.stats', 'Statistics') }}
            </h3>
            <div class="space-y-3 text-sm">
              <div class="flex justify-between">
                <span class="text-secondary">{{ $t('feedback.votes') }}:</span>
                <span class="font-medium">{{ feedback.votes }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-secondary">{{ $t('feedback.created', 'Created') }}:</span>
                <span class="font-medium">{{ formatDateShort(feedback.createdAt) }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-secondary">{{ $t('feedback.updated', 'Updated') }}:</span>
                <span class="font-medium">{{ formatDateShort(feedback.updatedAt) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Comments Section -->
      <div class="mt-12">
        <h2 class="text-xl font-semibold text-primary mb-6">
          {{ $t('comments.title') }} ({{ comments.length }})
        </h2>

        <!-- Comment Form -->
        <div class="card mb-8">
          <h3 class="text-lg font-medium text-primary mb-4">
            {{ $t('comments.addComment') }}
          </h3>
          <form @submit.prevent="submitComment" class="space-y-4">
            <div>
              <label for="comment" class="block text-sm font-medium text-secondary mb-2">
                {{ $t('comments.yourComment') }}
              </label>
              <textarea
                id="comment"
                v-model="newComment.content"
                rows="4"
                class="input"
                :placeholder="$t('comments.placeholder')"
                required
              ></textarea>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="commenterName" class="block text-sm font-medium text-secondary mb-2">
                  {{ $t('comments.name') }}
                </label>
                <input
                  id="commenterName"
                  v-model="newComment.author"
                  type="text"
                  class="input"
                  :placeholder="$t('comments.namePlaceholder')"
                  required
                />
              </div>

              <div>
                <label for="commenterEmail" class="block text-sm font-medium text-secondary mb-2">
                  {{ $t('comments.email') }}
                  <span class="text-tertiary text-xs ml-1">({{ $t('common.optional') }})</span>
                </label>
                <input
                  id="commenterEmail"
                  v-model="newComment.email"
                  type="email"
                  class="input"
                  :placeholder="$t('comments.emailPlaceholder')"
                />
              </div>
            </div>

            <div class="flex justify-end">
              <button
                type="submit"
                class="btn btn-primary"
                :disabled="isSubmittingComment"
              >
                {{ isSubmittingComment ? $t('common.submitting') : $t('comments.submit') }}
              </button>
            </div>
          </form>
        </div>

        <!-- Comments List -->
        <div v-if="comments.length > 0" class="space-y-6">
          <div
            v-for="comment in comments"
            :key="comment.id"
            class="card"
          >
            <div class="flex gap-4">
              <!-- Avatar -->
              <div class="flex-shrink-0">
                <div class="w-10 h-10 blue-accent flex items-center justify-center text-white font-medium">
                  {{ comment.author.charAt(0).toUpperCase() }}
                </div>
              </div>

              <!-- Comment Content -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center space-x-2 mb-2">
                  <h4 class="text-sm font-medium text-primary">{{ comment.author }}</h4>
                  <span class="text-xs text-tertiary">•</span>
                  <time class="text-xs text-tertiary" :datetime="comment.createdAt">
                    {{ formatDate(comment.createdAt) }}
                  </time>
                </div>

                <p class="text-secondary leading-relaxed whitespace-pre-wrap">
                  {{ comment.content }}
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty Comments State -->
        <div v-else class="text-center py-8">
          <div class="w-16 h-16 bg-secondary flex items-center justify-center mx-auto mb-4">
            <svg class="w-8 h-8 text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
            </svg>
          </div>
          <h3 class="text-lg font-medium text-primary mb-2">
            {{ $t('comments.noComments') }}
          </h3>
          <p class="text-secondary">
            {{ $t('comments.beFirst') }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import VoteButton from '~/components/VoteButton.vue'
import StatusBadge from '~/components/StatusBadge.vue'
import PriorityBadge from '~/components/PriorityBadge.vue'
import TagBadge from '~/components/TagBadge.vue'
import CategoryBadge from '~/components/CategoryBadge.vue'

const route = useRoute()
const config = useRuntimeConfig()
const { getFeedback, voteFeedback } = useFeedback()

const feedback = ref(null)
const isLoading = ref(true)
const error = ref(false)

// Comments data
const comments = ref([])
const isSubmittingComment = ref(false)
const newComment = ref({
  content: '',
  author: '',
  email: ''
})

const loadFeedback = async () => {
  isLoading.value = true
  error.value = false

  try {
    const feedbackId = parseInt(route.params.id)

    if (!feedbackId || isNaN(feedbackId)) {
      error.value = true
      return
    }

    const result = await getFeedback(feedbackId)

    if (result.success) {
      feedback.value = result.data
      // Load comments after feedback is loaded
      loadComments()
    } else {
      error.value = true
    }
  } catch (err) {
    console.error('Error loading feedback:', err)
    error.value = true
  } finally {
    isLoading.value = false
  }
}

// Load comments for this feedback
const loadComments = () => {
  // Mock comments data - in a real app, this would come from an API
  const mockComments = [
    {
      id: 1,
      content: "This is a great suggestion! I've been waiting for dark mode for a long time. It would really help with eye strain during late night work sessions.",
      author: "Sarah Chen",
      email: "<EMAIL>",
      createdAt: "2024-01-16T14:30:00Z"
    },
    {
      id: 2,
      content: "I agree with this request. Dark mode is essential for modern applications. Most users expect it nowadays.",
      author: "Mike Johnson",
      email: "<EMAIL>",
      createdAt: "2024-01-16T16:45:00Z"
    },
    {
      id: 3,
      content: "Would love to see this implemented soon! Maybe we could also have an auto-switch based on system preferences?",
      author: "Alex Rodriguez",
      email: "<EMAIL>",
      createdAt: "2024-01-17T09:15:00Z"
    }
  ]

  // Only show comments for feedback ID 1 (dark mode) as an example
  if (feedbackId.value === 1) {
    comments.value = mockComments
  } else {
    comments.value = []
  }
}

// Submit a new comment
const submitComment = async () => {
  if (isSubmittingComment.value) return

  isSubmittingComment.value = true

  try {
    // Mock comment submission - in a real app, this would be an API call
    const comment = {
      id: Date.now(),
      content: newComment.value.content.trim(),
      author: newComment.value.author.trim(),
      email: newComment.value.email?.trim() || null,
      createdAt: new Date().toISOString()
    }

    // Add to comments list
    comments.value.push(comment)

    // Reset form
    newComment.value = {
      content: '',
      author: '',
      email: ''
    }

    // Show success message (you could add a toast notification here)
    console.log('Comment submitted successfully')

  } catch (err) {
    console.error('Error submitting comment:', err)
    // Handle error (you could show an error message here)
  } finally {
    isSubmittingComment.value = false
  }
}

const handleVote = async (voteData) => {
  try {
    const result = await voteFeedback(voteData.feedbackId, voteData.voted)

    if (result.success && feedback.value) {
      feedback.value.votes = result.data.newVoteCount
      feedback.value.userVoted = voteData.voted
    } else {
      console.error('Vote error:', result.error)
    }
  } catch (error) {
    console.error('Error updating vote:', error)
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) {
    return 'Yesterday'
  } else if (diffDays < 7) {
    return `${diffDays} days ago`
  } else {
    return date.toLocaleDateString()
  }
}

const formatDateShort = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

onMounted(() => {
  loadFeedback()
})
</script>
