<template>
  <div>
    <!-- <PERSON><PERSON> Head -->
    <Head>
      <Title>{{ feedback?.title || 'Feedback' }} | voice.new</Title>
      <Meta name="description" :content="feedback?.content || 'View feedback details'" />
      <Meta name="keywords" content="feedback, user feedback, details, voice.new" />
      <Link rel="canonical" :href="`${$config.public.baseUrl}${$route.path}`" />
    </Head>

    <div v-if="isLoading" class="text-center py-16">
      <div class="w-12 h-12 border-4 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
      <p class="text-secondary">{{ $t('common.loading') }}</p>
    </div>

    <div v-else-if="error" class="text-center py-16">
      <div class="w-24 h-24 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
        <svg class="w-12 h-12 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
        </svg>
      </div>
      <h3 class="text-xl font-semibold text-primary mb-2">
        {{ $t('common.error') }}
      </h3>
      <p class="text-secondary mb-6">
        Feedback not found
      </p>
      <NuxtLink to="/" class="btn btn-primary">
        {{ $t('common.back') }}
      </NuxtLink>
    </div>

    <div v-else-if="feedback" class="max-w-4xl mx-auto">
      <!-- Back Button -->
      <div class="mb-6">
        <NuxtLink to="/" class="btn btn-ghost">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          {{ $t('common.back') }}
        </NuxtLink>
      </div>

      <!-- Feedback Content -->
      <div class="card">
        <div class="flex gap-6">
          <!-- Vote Button -->
          <div class="flex-shrink-0">
            <VoteButton 
              :votes="feedback.votes" 
              :voted="feedback.userVoted"
              :feedback-id="feedback.id"
              @vote="handleVote"
            />
          </div>
          
          <!-- Content -->
          <div class="flex-1 min-w-0">
            <h1 class="text-2xl md:text-3xl font-bold text-primary mb-4">
              {{ feedback.title }}
            </h1>
            
            <div class="flex items-center space-x-4 text-sm text-tertiary mb-6">
              <span>{{ feedback.author }}</span>
              <span>{{ formatDate(feedback.createdAt) }}</span>
              <span>{{ feedback.votes }} {{ $t('feedback.votes') }}</span>
            </div>
            
            <div class="prose prose-lg max-w-none">
              <p class="text-secondary leading-relaxed whitespace-pre-wrap">{{ feedback.content }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Comments Section (Future Enhancement) -->
      <div class="mt-12">
        <h2 class="text-xl font-semibold text-primary mb-6">Comments</h2>
        <div class="text-center py-8 text-secondary">
          <p>Comments feature coming soon...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import VoteButton from '~/components/VoteButton.vue'

const route = useRoute()
const config = useRuntimeConfig()

const feedback = ref(null)
const isLoading = ref(true)
const error = ref(false)

// Mock data for demonstration
const mockFeedback = {
  1: {
    id: 1,
    title: "Add dark mode support",
    content: "It would be great to have a dark mode option for better user experience during night time usage. This feature would help reduce eye strain and provide a more comfortable viewing experience in low-light environments.\n\nMany modern applications now include dark mode as a standard feature, and users have come to expect this functionality. The implementation could include:\n\n1. A toggle switch in the header\n2. System preference detection\n3. Persistent user preference storage\n4. Smooth transitions between themes\n\nThis would significantly improve the overall user experience and accessibility of the platform.",
    author: "John Doe",
    votes: 15,
    userVoted: false,
    createdAt: "2024-01-15T10:30:00Z"
  },
  2: {
    id: 2,
    title: "Improve mobile responsiveness",
    content: "The current mobile layout could be improved. Some buttons are too small and text is hard to read on smaller screens.",
    author: "Jane Smith",
    votes: 8,
    userVoted: true,
    createdAt: "2024-01-14T15:45:00Z"
  },
  3: {
    id: 3,
    title: "Add search functionality",
    content: "Users should be able to search through existing feedback to avoid duplicates and find relevant suggestions.",
    author: "Mike Johnson",
    votes: 23,
    userVoted: false,
    createdAt: "2024-01-13T09:20:00Z"
  }
}

const loadFeedback = async () => {
  isLoading.value = true
  error.value = false
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const feedbackId = parseInt(route.params.id)
    const feedbackData = mockFeedback[feedbackId]
    
    if (feedbackData) {
      feedback.value = feedbackData
    } else {
      error.value = true
    }
  } catch (err) {
    console.error('Error loading feedback:', err)
    error.value = true
  } finally {
    isLoading.value = false
  }
}

const handleVote = async (voteData) => {
  try {
    if (feedback.value) {
      if (voteData.voted && !feedback.value.userVoted) {
        feedback.value.votes += 1
        feedback.value.userVoted = true
      } else if (!voteData.voted && feedback.value.userVoted) {
        feedback.value.votes -= 1
        feedback.value.userVoted = false
      }
    }
    
    console.log('Vote updated:', voteData)
  } catch (error) {
    console.error('Error updating vote:', error)
  }
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

onMounted(() => {
  loadFeedback()
})
</script>
