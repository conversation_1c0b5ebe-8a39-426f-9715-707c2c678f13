<template>
  <div>
    <!-- <PERSON><PERSON> Head -->
    <Head>
      <Title>{{ $t('auth.loginTitle') }} | voice.new</Title>
      <Meta name="description" :content="$t('auth.loginDescription')" />
      <Meta name="keywords" content="login, sign in, user account, voice.new" />
      <Link rel="canonical" :href="`${$config.public.baseUrl}${$route.path}`" />
    </Head>

    <div class="max-w-md mx-auto">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-primary mb-4">
          {{ $t('auth.loginTitle') }}
        </h1>
        <p class="text-secondary">
          {{ $t('auth.loginDescription') }}
        </p>
      </div>

      <!-- Login Form -->
      <div class="card">
        <form @submit.prevent="handleLogin" class="space-y-6">
          <!-- Email Field -->
          <div>
            <label for="email" class="block text-sm font-medium text-primary mb-2">
              {{ $t('auth.email') }}
            </label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              class="input"
              :class="{ 'border-red-500': errors.email }"
              placeholder="<EMAIL>"
              required
            />
            <p v-if="errors.email" class="mt-1 text-sm text-red-600">
              {{ errors.email }}
            </p>
          </div>

          <!-- Password Field -->
          <div>
            <label for="password" class="block text-sm font-medium text-primary mb-2">
              {{ $t('auth.password') }}
            </label>
            <input
              id="password"
              v-model="form.password"
              type="password"
              class="input"
              :class="{ 'border-red-500': errors.password }"
              placeholder="••••••••"
              required
            />
            <p v-if="errors.password" class="mt-1 text-sm text-red-600">
              {{ errors.password }}
            </p>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            class="w-full btn btn-primary"
            :disabled="isLoading"
          >
            {{ isLoading ? $t('common.loading') : $t('auth.loginButton') }}
          </button>
        </form>

        <!-- Register Link -->
        <div class="mt-6 text-center">
          <p class="text-sm text-secondary">
            {{ $t('auth.registerLink') }}
          </p>
          <NuxtLink to="/register" class="text-primary hover:underline">
            {{ $t('auth.register') }}
          </NuxtLink>
        </div>
      </div>

      <!-- Success Message -->
      <div v-if="showSuccess" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <p class="text-green-800">{{ $t('auth.loginSuccess') }}</p>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="showError" class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <p class="text-red-800">{{ $t('auth.loginError') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const config = useRuntimeConfig()
const router = useRouter()

// Form data
const form = reactive({
  email: '',
  password: ''
})

// Form state
const isLoading = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const errors = reactive({})

// Validation
const validateForm = () => {
  const newErrors = {}
  
  if (!form.email.trim()) {
    newErrors.email = 'Email is required'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    newErrors.email = 'Please enter a valid email'
  }
  
  if (!form.password.trim()) {
    newErrors.password = 'Password is required'
  } else if (form.password.length < 6) {
    newErrors.password = 'Password must be at least 6 characters'
  }
  
  Object.keys(errors).forEach(key => delete errors[key])
  Object.assign(errors, newErrors)
  
  return Object.keys(newErrors).length === 0
}

// Handle login
const handleLogin = async () => {
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  showSuccess.value = false
  showError.value = false
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Mock successful login
    console.log('Login attempt:', form)
    
    showSuccess.value = true
    
    // Redirect to home after 1 second
    setTimeout(() => {
      router.push('/')
    }, 1000)
    
  } catch (error) {
    console.error('Login error:', error)
    showError.value = true
  } finally {
    isLoading.value = false
  }
}
</script>
