<template>
  <div>
    <!-- SEO Head -->
    <Head>
      <Title>{{ $t('feedback.title') }} | voice.new</Title>
      <Meta name="description" :content="$t('feedback.description')" />
      <Meta name="keywords" content="submit feedback, user feedback, suggestions, voice.new" />
      <Link rel="canonical" :href="`${$config.public.baseUrl}${$route.path}`" />
    </Head>

    <div class="max-w-2xl mx-auto">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl md:text-4xl font-bold text-primary mb-4">
          {{ $t('feedback.title') }}
        </h1>
        <p class="text-lg text-secondary">
          {{ $t('feedback.description') }}
        </p>
      </div>

      <!-- Form -->
      <div class="card">
        <form @submit.prevent="submitFeedback" class="space-y-6">
          <!-- Title Field -->
          <div>
            <label for="title" class="block text-sm font-medium text-primary mb-2">
              {{ $t('feedback.titleLabel') }}
            </label>
            <input
              id="title"
              v-model="form.title"
              type="text"
              class="input"
              :class="{ 'border-red-500': errors.title }"
              :placeholder="$t('feedback.titlePlaceholder')"
              required
            />
            <p v-if="errors.title" class="mt-1 text-sm text-red-600">
              {{ errors.title }}
            </p>
          </div>

          <!-- Content Field -->
          <div>
            <label for="content" class="block text-sm font-medium text-primary mb-2">
              {{ $t('feedback.contentLabel') }}
            </label>
            <textarea
              id="content"
              v-model="form.content"
              rows="6"
              class="input resize-none"
              :class="{ 'border-red-500': errors.content }"
              :placeholder="$t('feedback.contentPlaceholder')"
              required
            ></textarea>
            <p v-if="errors.content" class="mt-1 text-sm text-red-600">
              {{ errors.content }}
            </p>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end space-x-4">
            <NuxtLink to="/" class="btn btn-secondary">
              {{ $t('common.cancel') }}
            </NuxtLink>
            <button
              type="submit"
              class="btn btn-primary"
              :disabled="isSubmitting"
            >
              {{ isSubmitting ? $t('feedback.submitting') : $t('feedback.submit') }}
            </button>
          </div>
        </form>
      </div>

      <!-- Success Message -->
      <div v-if="showSuccess" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <p class="text-green-800">{{ $t('feedback.success') }}</p>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="showError" class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <p class="text-red-800">{{ $t('feedback.error') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const config = useRuntimeConfig()
const router = useRouter()

// Form data
const form = reactive({
  title: '',
  content: ''
})

// Form state
const isSubmitting = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const errors = reactive({})

// Validation
const validateForm = () => {
  const newErrors = {}
  
  if (!form.title.trim()) {
    newErrors.title = 'Title is required'
  } else if (form.title.length < 5) {
    newErrors.title = 'Title must be at least 5 characters'
  } else if (form.title.length > 100) {
    newErrors.title = 'Title must be less than 100 characters'
  }
  
  if (!form.content.trim()) {
    newErrors.content = 'Description is required'
  } else if (form.content.length < 10) {
    newErrors.content = 'Description must be at least 10 characters'
  } else if (form.content.length > 1000) {
    newErrors.content = 'Description must be less than 1000 characters'
  }
  
  Object.keys(errors).forEach(key => delete errors[key])
  Object.assign(errors, newErrors)
  
  return Object.keys(newErrors).length === 0
}

// Submit feedback
const submitFeedback = async () => {
  if (!validateForm()) {
    return
  }
  
  isSubmitting.value = true
  showSuccess.value = false
  showError.value = false
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mock successful submission
    console.log('Feedback submitted:', form)
    
    showSuccess.value = true
    
    // Reset form
    form.title = ''
    form.content = ''
    
    // Redirect to home after 2 seconds
    setTimeout(() => {
      router.push('/')
    }, 2000)
    
  } catch (error) {
    console.error('Error submitting feedback:', error)
    showError.value = true
  } finally {
    isSubmitting.value = false
  }
}

// Check if user is logged in (mock)
const isLoggedIn = ref(true) // Replace with actual auth check

// Redirect to login if not authenticated
if (!isLoggedIn.value) {
  await navigateTo('/login')
}
</script>
