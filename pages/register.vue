<template>
  <div>
    <!-- SEO Head -->
    <Head>
      <Title>{{ $t('auth.registerTitle') }} | voice.new</Title>
      <Meta name="description" :content="$t('auth.registerDescription')" />
      <Meta name="keywords" content="register, sign up, create account, voice.new" />
      <Link rel="canonical" :href="`${$config.public.baseUrl}${$route.path}`" />
    </Head>

    <div class="max-w-md mx-auto">
      <!-- Header -->
      <div class="text-center mb-8">
        <h1 class="text-3xl font-bold text-primary mb-4">
          {{ $t('auth.registerTitle') }}
        </h1>
        <p class="text-secondary">
          {{ $t('auth.registerDescription') }}
        </p>
      </div>

      <!-- Register Form -->
      <div class="card">
        <form @submit.prevent="handleRegister" class="space-y-6">
          <!-- Name Field -->
          <div>
            <label for="name" class="block text-sm font-medium text-primary mb-2">
              {{ $t('auth.name') }}
            </label>
            <input
              id="name"
              v-model="form.name"
              type="text"
              class="input"
              :class="{ 'border-red-500': errors.name }"
              placeholder="Your full name"
              required
            />
            <p v-if="errors.name" class="mt-1 text-sm text-red-600">
              {{ errors.name }}
            </p>
          </div>

          <!-- Email Field -->
          <div>
            <label for="email" class="block text-sm font-medium text-primary mb-2">
              {{ $t('auth.email') }}
            </label>
            <input
              id="email"
              v-model="form.email"
              type="email"
              class="input"
              :class="{ 'border-red-500': errors.email }"
              placeholder="<EMAIL>"
              required
            />
            <p v-if="errors.email" class="mt-1 text-sm text-red-600">
              {{ errors.email }}
            </p>
          </div>

          <!-- Password Field -->
          <div>
            <label for="password" class="block text-sm font-medium text-primary mb-2">
              {{ $t('auth.password') }}
            </label>
            <input
              id="password"
              v-model="form.password"
              type="password"
              class="input"
              :class="{ 'border-red-500': errors.password }"
              placeholder="••••••••"
              required
            />
            <p v-if="errors.password" class="mt-1 text-sm text-red-600">
              {{ errors.password }}
            </p>
          </div>

          <!-- Confirm Password Field -->
          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-primary mb-2">
              {{ $t('auth.confirmPassword') }}
            </label>
            <input
              id="confirmPassword"
              v-model="form.confirmPassword"
              type="password"
              class="input"
              :class="{ 'border-red-500': errors.confirmPassword }"
              placeholder="••••••••"
              required
            />
            <p v-if="errors.confirmPassword" class="mt-1 text-sm text-red-600">
              {{ errors.confirmPassword }}
            </p>
          </div>

          <!-- Submit Button -->
          <button
            type="submit"
            class="w-full btn btn-primary"
            :disabled="isLoading"
          >
            {{ isLoading ? $t('common.loading') : $t('auth.registerButton') }}
          </button>
        </form>

        <!-- Login Link -->
        <div class="mt-6 text-center">
          <p class="text-sm text-secondary">
            {{ $t('auth.loginLink') }}
          </p>
          <NuxtLink to="/login" class="text-primary hover:underline">
            {{ $t('auth.login') }}
          </NuxtLink>
        </div>
      </div>

      <!-- Success Message -->
      <div v-if="showSuccess" class="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <p class="text-green-800">{{ $t('auth.registerSuccess') }}</p>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="showError" class="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
        <div class="flex items-center">
          <svg class="w-5 h-5 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <p class="text-red-800">{{ $t('auth.registerError') }}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue'

const config = useRuntimeConfig()
const router = useRouter()

// Form data
const form = reactive({
  name: '',
  email: '',
  password: '',
  confirmPassword: ''
})

// Form state
const isLoading = ref(false)
const showSuccess = ref(false)
const showError = ref(false)
const errors = reactive({})

// Validation
const validateForm = () => {
  const newErrors = {}
  
  if (!form.name.trim()) {
    newErrors.name = 'Name is required'
  } else if (form.name.length < 2) {
    newErrors.name = 'Name must be at least 2 characters'
  }
  
  if (!form.email.trim()) {
    newErrors.email = 'Email is required'
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.email)) {
    newErrors.email = 'Please enter a valid email'
  }
  
  if (!form.password.trim()) {
    newErrors.password = 'Password is required'
  } else if (form.password.length < 6) {
    newErrors.password = 'Password must be at least 6 characters'
  }
  
  if (!form.confirmPassword.trim()) {
    newErrors.confirmPassword = 'Please confirm your password'
  } else if (form.password !== form.confirmPassword) {
    newErrors.confirmPassword = 'Passwords do not match'
  }
  
  Object.keys(errors).forEach(key => delete errors[key])
  Object.assign(errors, newErrors)
  
  return Object.keys(newErrors).length === 0
}

// Handle registration
const handleRegister = async () => {
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  showSuccess.value = false
  showError.value = false
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mock successful registration
    console.log('Registration attempt:', form)
    
    showSuccess.value = true
    
    // Redirect to login after 2 seconds
    setTimeout(() => {
      router.push('/login')
    }, 2000)
    
  } catch (error) {
    console.error('Registration error:', error)
    showError.value = true
  } finally {
    isLoading.value = false
  }
}
</script>
