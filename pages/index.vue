<template>
  <div>
    <!-- SEO Head -->
    <Head>
      <Title>{{ $t('home.title') }} | voice.new</Title>
      <Meta name="description" :content="$t('home.description')" />
      <Meta name="keywords" content="feedback, user feedback, community, suggestions, voice.new" />
      <Link rel="canonical" :href="`${$config.public.baseUrl}${$route.path}`" />
    </Head>

    <!-- Hero Section -->
    <div class="hero-section -mx-4 px-4 py-16 mb-12">
      <div class="container-narrow">
        <div class="text-center">
          <h1 class="text-4xl md:text-5xl font-bold mb-6">
            {{ $t('home.title') }}
          </h1>
          <p class="text-xl text-white text-opacity-90 max-w-2xl mx-auto mb-8">
            {{ $t('home.description') }}
          </p>
          <NuxtLink to="/submit" class="btn btn-secondary bg-white text-primary hover:bg-gray-100 btn-lg">
            {{ $t('nav.submit') }}
          </NuxtLink>
        </div>
      </div>
    </div>

    <!-- Content Section -->
    <div class="container-narrow">
      <!-- Filters and Sort -->
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div class="flex items-center space-x-4">
          <h2 class="text-2xl font-semibold text-primary">
            {{ $t('home.subtitle') }}
          </h2>
        </div>

        <div class="flex items-center space-x-2">
          <select v-model="sortBy" class="input text-sm">
            <option value="newest">{{ $t('common.newest') }}</option>
            <option value="oldest">{{ $t('common.oldest') }}</option>
            <option value="mostVoted">{{ $t('common.mostVoted') }}</option>
          </select>
        </div>
      </div>

      <!-- Feedback List -->
      <div v-if="feedbackList.length > 0" class="space-y-6">
        <FeedbackCard
          v-for="feedback in sortedFeedback"
          :key="feedback.id"
          :feedback="feedback"
          @vote="handleVote"
        />

        <!-- Load More Button -->
        <div v-if="hasMore" class="text-center pt-8">
          <button @click="loadMore" class="btn btn-secondary" :disabled="isLoading">
            {{ isLoading ? $t('common.loading') : $t('home.loadMore') }}
          </button>
        </div>
      </div>

      <!-- Empty State -->
      <div v-else class="text-center py-16">
        <div class="w-24 h-24 blue-accent-light flex items-center justify-center mx-auto mb-6">
          <svg class="w-12 h-12 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
          </svg>
        </div>
        <h3 class="text-xl font-semibold text-primary mb-2">
          {{ $t('home.noFeedback') }}
        </h3>
        <p class="text-secondary mb-6">
          {{ $t('home.description') }}
        </p>
        <NuxtLink to="/submit" class="btn btn-primary">
          {{ $t('nav.submit') }}
        </NuxtLink>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import FeedbackCard from '~/components/FeedbackCard.vue'

const config = useRuntimeConfig()
const { getFeedbackList, voteFeedback } = useFeedback()

// Reactive data
const feedbackList = ref([])
const isLoading = ref(false)
const hasMore = ref(true)
const sortBy = ref('newest')
const currentPage = ref(1)
const error = ref('')

// Computed properties
const sortedFeedback = computed(() => {
  return feedbackList.value // API already handles sorting
})

// Methods
const loadFeedback = async (reset = false) => {
  if (reset) {
    currentPage.value = 1
    feedbackList.value = []
  }

  isLoading.value = true
  error.value = ''

  try {
    const result = await getFeedbackList({
      page: currentPage.value,
      limit: 10,
      sortBy: sortBy.value
    })

    if (result.success) {
      if (reset) {
        feedbackList.value = result.data.data
      } else {
        feedbackList.value.push(...result.data.data)
      }
      hasMore.value = result.data.pagination.hasMore
    } else {
      error.value = result.error
    }
  } catch (err) {
    console.error('Error loading feedback:', err)
    error.value = 'Failed to load feedback'
  } finally {
    isLoading.value = false
  }
}

const loadMore = async () => {
  if (!hasMore.value || isLoading.value) return

  currentPage.value += 1
  await loadFeedback(false)
}

const handleVote = async (voteData) => {
  try {
    const result = await voteFeedback(voteData.feedbackId, voteData.voted)

    if (result.success) {
      // Update local state
      const feedback = feedbackList.value.find(f => f.id === voteData.feedbackId)
      if (feedback) {
        feedback.votes = result.data.newVoteCount
        feedback.userVoted = voteData.voted
      }
    } else {
      console.error('Vote error:', result.error)
    }
  } catch (error) {
    console.error('Error updating vote:', error)
  }
}

// Watch for sort changes
watch(sortBy, () => {
  loadFeedback(true)
})

// Lifecycle
onMounted(() => {
  loadFeedback()
})
</script>
