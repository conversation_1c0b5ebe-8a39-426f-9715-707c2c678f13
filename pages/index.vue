<template>
  <div>
    <Head>
      <Title>{{ $t('landing.title') }} | voice.new</Title>
      <Meta name="description" :content="$t('landing.description')" />
      <Meta name="keywords" content="feedback collection, user feedback, SAAS, voice.new, customer feedback" />
      <Link rel="canonical" :href="`${$config.public.baseUrl}${$route.path}`" />
    </Head>

    <!-- Hero Section -->
    <section class="min-h-screen flex items-center justify-center px-4 bg-gray-50 relative overflow-hidden">
      <!-- Rich animated background -->
      <div class="absolute inset-0">
        <!-- Subtle radial gradient overlay -->
        <div class="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-blue-50/20"></div>

        <!-- Large floating shapes with animation -->
        <div class="absolute top-20 left-20 w-64 h-64 bg-blue-100/20 rounded-full blur-3xl animate-float"></div>
        <div class="absolute bottom-20 right-20 w-80 h-80 bg-blue-200/15 rounded-full blur-3xl animate-float" style="animation-delay: -2s;"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-50/25 rounded-full blur-3xl animate-float" style="animation-delay: -4s;"></div>

        <!-- Medium floating shapes -->
        <div class="absolute top-40 right-40 w-32 h-32 bg-blue-300/10 rounded-full blur-2xl animate-drift" style="animation-delay: -1s;"></div>
        <div class="absolute bottom-40 left-40 w-40 h-40 bg-blue-400/8 rounded-full blur-2xl animate-drift" style="animation-delay: -3s;"></div>
        <div class="absolute top-60 left-1/3 w-24 h-24 bg-blue-200/12 rounded-full blur-xl animate-drift" style="animation-delay: -5s;"></div>

        <!-- Small floating shapes -->
        <div class="absolute top-28 left-3/4 w-16 h-16 bg-blue-300/8 rounded-full blur-xl animate-float" style="animation-delay: -2.5s;"></div>
        <div class="absolute bottom-28 right-1/4 w-20 h-20 bg-blue-400/6 rounded-full blur-xl animate-float" style="animation-delay: -4.5s;"></div>
        <div class="absolute top-3/4 right-1/3 w-12 h-12 bg-blue-200/10 rounded-full blur-lg animate-drift" style="animation-delay: -6s;"></div>

        <!-- Geometric elements with various animations -->
        <div class="absolute top-32 right-1/4 w-4 h-4 border border-blue-200 opacity-60 animate-rotate-slow"></div>
        <div class="absolute bottom-40 left-1/4 w-3 h-3 bg-blue-300 rounded-full opacity-40 animate-bounce" style="animation-duration: 3s;"></div>

        <!-- Additional geometric shapes -->
        <div class="absolute top-20 right-1/3 w-3 h-3 border border-blue-300 opacity-50 animate-rotate-slow" style="animation-delay: -3s;"></div>
        <div class="absolute bottom-20 left-1/3 w-2 h-2 bg-blue-400 opacity-40 animate-pulse-soft" style="animation-delay: -4s;"></div>

        <!-- Additional small decorative elements -->
        <div class="absolute top-24 left-1/2 w-1 h-1 bg-blue-400 rounded-full opacity-50 animate-ping" style="animation-duration: 4s;"></div>
        <div class="absolute bottom-32 right-1/3 w-1.5 h-1.5 bg-blue-500 rounded-full opacity-40 animate-ping" style="animation-duration: 5s; animation-delay: -1s;"></div>
        <div class="absolute top-2/3 right-20 w-1 h-1 bg-blue-300 rounded-full opacity-60 animate-ping" style="animation-duration: 3s; animation-delay: -2s;"></div>

        <!-- Triangular shapes -->
        <div class="absolute top-48 left-32 w-0 h-0 border-l-4 border-r-4 border-b-6 border-transparent border-b-blue-200 opacity-30 animate-pulse-soft" style="animation-delay: -1.5s;"></div>
        <div class="absolute bottom-48 right-48 w-0 h-0 border-l-3 border-r-3 border-b-5 border-transparent border-b-blue-300 opacity-40 animate-pulse-soft" style="animation-delay: -3.5s;"></div>
        <div class="absolute top-36 right-20 w-0 h-0 border-l-2 border-r-2 border-b-4 border-transparent border-b-blue-400 opacity-35 animate-rotate-slow" style="animation-delay: -2s;"></div>

        <!-- Complex geometric patterns -->
        <div class="absolute top-52 right-1/3 w-6 h-6 border-2 border-blue-200 opacity-25 animate-rotate-slow" style="animation-delay: -5s;"></div>

        <!-- Hexagonal shapes -->
        <div class="absolute top-44 left-1/2 w-3 h-3 bg-blue-200 opacity-30 transform rotate-45 animate-drift" style="animation-delay: -7s; clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);"></div>
        <div class="absolute bottom-44 right-1/2 w-2 h-2 bg-blue-400 opacity-35 animate-rotate-slow" style="animation-delay: -8s; clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);"></div>

        <!-- Curved elements -->
        <div class="absolute top-64 right-1/4 w-8 h-8 border border-blue-300 opacity-20 rounded-full animate-pulse-soft" style="animation-delay: -9s;"></div>
        <div class="absolute bottom-64 left-1/4 w-6 h-6 border-2 border-blue-200 opacity-25 rounded-full animate-drift" style="animation-delay: -10s;"></div>

        <!-- Subtle grid pattern -->
        <svg class="absolute inset-0 w-full h-full opacity-8" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="grid" x="0" y="0" width="50" height="50" patternUnits="userSpaceOnUse">
              <circle cx="25" cy="25" r="0.5" fill="#3B82F6" opacity="0.2"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
        </svg>

        <!-- Animated line patterns -->
        <svg class="absolute inset-0 w-full h-full opacity-5" viewBox="0 0 1000 1000" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern id="lines" x="0" y="0" width="100" height="100" patternUnits="userSpaceOnUse">
              <line x1="0" y1="50" x2="100" y2="50" stroke="#3B82F6" stroke-width="0.5" opacity="0.3">
                <animate attributeName="opacity" values="0.1;0.4;0.1" dur="6s" repeatCount="indefinite"/>
              </line>
              <line x1="50" y1="0" x2="50" y2="100" stroke="#3B82F6" stroke-width="0.5" opacity="0.3">
                <animate attributeName="opacity" values="0.4;0.1;0.4" dur="6s" repeatCount="indefinite"/>
              </line>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#lines)" />
        </svg>
      </div>

      <div class="max-w-5xl mx-auto text-center relative z-10">
        <div class="mb-16">
          <!-- Badge -->
          <div class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-700 rounded-full text-sm font-medium mb-8">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span v-if="$i18n.locale === 'en'">New: Real-time feedback collection</span>
            <span v-else>新功能：实时反馈收集</span>
          </div>

          <h1 class="text-6xl md:text-8xl font-bold mb-8 leading-tight tracking-tight">
            <span v-if="$i18n.locale === 'en'" class="text-gray-500 text-4xl md:text-5xl font-medium block mb-2">Where Every</span>
            <span v-if="$i18n.locale === 'en'" class="text-blue-600 block">Voice Matters</span>
            <span v-else class="text-gray-500 text-4xl md:text-5xl font-medium block mb-2">每个</span>
            <span v-else class="text-blue-600 block">声音都重要</span>
          </h1>
          <p class="text-xl md:text-2xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            {{ $t('landing.hero.subtitle') }}
          </p>
        </div>

        <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-24">
          <NuxtLink to="/register" class="bg-blue-600 text-white px-8 py-4 text-lg font-semibold hover:bg-blue-700 transition-all duration-200 transform hover:scale-105 shadow-lg hover:shadow-xl min-w-[200px]">
            {{ $t('landing.hero.getStarted') }}
          </NuxtLink>
          <NuxtLink to="/demo" class="text-blue-600 border-2 border-blue-200 px-8 py-4 text-lg font-semibold hover:border-blue-300 hover:bg-blue-50 transition-all duration-200 min-w-[200px]">
            View Demo
          </NuxtLink>
        </div>

        <!-- Feature Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <div class="bg-white/80 backdrop-blur-sm border border-white/50 p-8 rounded-2xl hover:bg-white/90 hover:border-blue-200/50 hover:shadow-xl hover:shadow-blue-100/20 transition-all duration-500 group relative overflow-hidden">
            <!-- Card background decoration -->
            <div class="absolute top-0 right-0 w-20 h-20 bg-blue-50/50 rounded-full blur-2xl transform translate-x-10 -translate-y-10"></div>

            <div class="relative z-10">
              <div class="w-14 h-14 bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center mx-auto mb-6 rounded-xl group-hover:from-blue-100 group-hover:to-blue-200 transition-all duration-300 shadow-sm">
                <svg class="w-7 h-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('landing.features.collect.title') }}</h3>
              <p class="text-gray-600 leading-relaxed">{{ $t('landing.features.collect.description') }}</p>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm border border-white/50 p-8 rounded-2xl hover:bg-white/90 hover:border-blue-200/50 hover:shadow-xl hover:shadow-blue-100/20 transition-all duration-500 group relative overflow-hidden">
            <!-- Card background decoration -->
            <div class="absolute top-0 right-0 w-20 h-20 bg-blue-50/50 rounded-full blur-2xl transform translate-x-10 -translate-y-10"></div>

            <div class="relative z-10">
              <div class="w-14 h-14 bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center mx-auto mb-6 rounded-xl group-hover:from-blue-100 group-hover:to-blue-200 transition-all duration-300 shadow-sm">
                <svg class="w-7 h-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12" />
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('landing.features.vote.title') }}</h3>
              <p class="text-gray-600 leading-relaxed">{{ $t('landing.features.vote.description') }}</p>
            </div>
          </div>

          <div class="bg-white/80 backdrop-blur-sm border border-white/50 p-8 rounded-2xl hover:bg-white/90 hover:border-blue-200/50 hover:shadow-xl hover:shadow-blue-100/20 transition-all duration-500 group relative overflow-hidden">
            <!-- Card background decoration -->
            <div class="absolute top-0 right-0 w-20 h-20 bg-blue-50/50 rounded-full blur-2xl transform translate-x-10 -translate-y-10"></div>

            <div class="relative z-10">
              <div class="w-14 h-14 bg-gradient-to-br from-blue-50 to-blue-100 flex items-center justify-center mx-auto mb-6 rounded-xl group-hover:from-blue-100 group-hover:to-blue-200 transition-all duration-300 shadow-sm">
                <svg class="w-7 h-7 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <h3 class="text-xl font-bold text-gray-900 mb-4">{{ $t('landing.features.analyze.title') }}</h3>
              <p class="text-gray-600 leading-relaxed">{{ $t('landing.features.analyze.description') }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-24 bg-gradient-to-r from-blue-600 via-blue-700 to-blue-600 relative overflow-hidden">
      <!-- Background decoration -->
      <div class="absolute inset-0">
        <div class="absolute top-10 left-20 w-32 h-32 bg-white/10 rounded-full blur-2xl"></div>
        <div class="absolute bottom-10 right-20 w-40 h-40 bg-white/5 rounded-full blur-3xl"></div>
        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white/5 rounded-full blur-3xl"></div>

        <!-- Geometric elements -->
        <div class="absolute top-20 right-1/4 w-2 h-2 bg-white/30 rounded-full"></div>
        <div class="absolute bottom-20 left-1/4 w-1 h-1 bg-white/40 rounded-full"></div>
        <div class="absolute top-1/3 right-20 w-1 h-6 bg-white/20"></div>
      </div>

      <div class="max-w-3xl mx-auto text-center px-4 relative z-10">
        <h2 class="text-4xl md:text-5xl font-bold text-white mb-6 whitespace-pre-line">
          {{ $t('landing.cta.title') }}
        </h2>
        <p class="text-xl text-blue-100 mb-10 leading-relaxed">
          {{ $t('landing.cta.subtitle') }}
        </p>
        <NuxtLink to="/register" class="bg-white text-blue-600 px-10 py-4 text-lg font-semibold hover:bg-blue-50 hover:shadow-lg transition-all duration-300 inline-block transform hover:scale-105">
          {{ $t('landing.cta.button') }}
        </NuxtLink>
      </div>
    </section>
  </div>
</template>

<script setup>
// Landing page logic can be added here if needed
</script>
