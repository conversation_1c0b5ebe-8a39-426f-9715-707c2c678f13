<template>
  <div>
    <Head>
      <Title>{{ $t('landing.title') }} | voice.new</Title>
      <Meta name="description" :content="$t('landing.description')" />
      <Meta name="keywords" content="feedback collection, user feedback, SAAS, voice.new, customer feedback" />
      <Link rel="canonical" :href="`${$config.public.baseUrl}${$route.path}`" />
    </Head>

    <!-- Hero Section -->
    <section class="hero-section -mx-4 px-4 py-20">
      <div class="container-narrow text-center">
        <h1 class="text-5xl md:text-6xl font-bold mb-6">
          {{ $t('landing.hero.title') }}
        </h1>
        <p class="text-xl text-white text-opacity-90 max-w-3xl mx-auto mb-8">
          {{ $t('landing.hero.subtitle') }}
        </p>
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <NuxtLink to="/register" class="btn bg-white text-primary hover:bg-gray-100 btn-lg">
            {{ $t('landing.hero.getStarted') }}
          </NuxtLink>
          <NuxtLink to="/login" class="btn border-2 border-white text-white hover:bg-white hover:text-primary btn-lg">
            {{ $t('landing.hero.signIn') }}
          </NuxtLink>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="py-20">
      <div class="container-narrow">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">
            {{ $t('landing.features.title') }}
          </h2>
          <p class="text-lg text-secondary max-w-2xl mx-auto">
            {{ $t('landing.features.subtitle') }}
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="w-16 h-16 blue-accent-light flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-primary mb-2">
              {{ $t('landing.features.collect.title') }}
            </h3>
            <p class="text-secondary">
              {{ $t('landing.features.collect.description') }}
            </p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 blue-accent-light flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-primary mb-2">
              {{ $t('landing.features.vote.title') }}
            </h3>
            <p class="text-secondary">
              {{ $t('landing.features.vote.description') }}
            </p>
          </div>

          <div class="text-center">
            <div class="w-16 h-16 blue-accent-light flex items-center justify-center mx-auto mb-4">
              <svg class="w-8 h-8 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            </div>
            <h3 class="text-xl font-semibold text-primary mb-2">
              {{ $t('landing.features.analyze.title') }}
            </h3>
            <p class="text-secondary">
              {{ $t('landing.features.analyze.description') }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- How it Works Section -->
    <section class="py-20 bg-secondary">
      <div class="container-narrow">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">
            {{ $t('landing.howItWorks.title') }}
          </h2>
          <p class="text-lg text-secondary max-w-2xl mx-auto">
            {{ $t('landing.howItWorks.subtitle') }}
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <div class="text-center">
            <div class="w-12 h-12 blue-accent flex items-center justify-center mx-auto mb-4 text-white font-bold text-lg">
              1
            </div>
            <h3 class="text-lg font-semibold text-primary mb-2">
              {{ $t('landing.howItWorks.step1.title') }}
            </h3>
            <p class="text-secondary">
              {{ $t('landing.howItWorks.step1.description') }}
            </p>
          </div>

          <div class="text-center">
            <div class="w-12 h-12 blue-accent flex items-center justify-center mx-auto mb-4 text-white font-bold text-lg">
              2
            </div>
            <h3 class="text-lg font-semibold text-primary mb-2">
              {{ $t('landing.howItWorks.step2.title') }}
            </h3>
            <p class="text-secondary">
              {{ $t('landing.howItWorks.step2.description') }}
            </p>
          </div>

          <div class="text-center">
            <div class="w-12 h-12 blue-accent flex items-center justify-center mx-auto mb-4 text-white font-bold text-lg">
              3
            </div>
            <h3 class="text-lg font-semibold text-primary mb-2">
              {{ $t('landing.howItWorks.step3.title') }}
            </h3>
            <p class="text-secondary">
              {{ $t('landing.howItWorks.step3.description') }}
            </p>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-20">
      <div class="container-narrow text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-primary mb-4">
          {{ $t('landing.cta.title') }}
        </h2>
        <p class="text-lg text-secondary max-w-2xl mx-auto mb-8">
          {{ $t('landing.cta.subtitle') }}
        </p>
        <NuxtLink to="/register" class="btn btn-primary btn-lg">
          {{ $t('landing.cta.button') }}
        </NuxtLink>
      </div>
    </section>
  </div>
</template>

<script setup>
// Landing page logic can be added here if needed
</script>
