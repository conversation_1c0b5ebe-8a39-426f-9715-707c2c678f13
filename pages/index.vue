<template>
  <div>
    <!-- SEO Head -->
    <Head>
      <Title>{{ $t('home.title') }} | voice.new</Title>
      <Meta name="description" :content="$t('home.description')" />
      <Meta name="keywords" content="feedback, user feedback, community, suggestions, voice.new" />
      <Link rel="canonical" :href="`${$config.public.baseUrl}${$route.path}`" />
    </Head>

    <!-- Hero Section -->
    <div class="text-center mb-12">
      <h1 class="text-4xl md:text-5xl font-bold text-primary mb-4">
        {{ $t('home.title') }}
      </h1>
      <p class="text-xl text-secondary max-w-2xl mx-auto mb-8">
        {{ $t('home.description') }}
      </p>
      <NuxtLink to="/submit" class="btn btn-primary btn-lg">
        {{ $t('nav.submit') }}
      </NuxtLink>
    </div>

    <!-- Filters and Sort -->
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
      <div class="flex items-center space-x-4">
        <h2 class="text-2xl font-semibold text-primary">
          {{ $t('home.subtitle') }}
        </h2>
      </div>

      <div class="flex items-center space-x-2">
        <select v-model="sortBy" class="input text-sm">
          <option value="newest">{{ $t('common.newest') }}</option>
          <option value="oldest">{{ $t('common.oldest') }}</option>
          <option value="mostVoted">{{ $t('common.mostVoted') }}</option>
        </select>
      </div>
    </div>

    <!-- Feedback List -->
    <div v-if="feedbackList.length > 0" class="space-y-6">
      <FeedbackCard
        v-for="feedback in sortedFeedback"
        :key="feedback.id"
        :feedback="feedback"
        @vote="handleVote"
      />

      <!-- Load More Button -->
      <div v-if="hasMore" class="text-center pt-8">
        <button @click="loadMore" class="btn btn-secondary" :disabled="isLoading">
          {{ isLoading ? $t('common.loading') : $t('home.loadMore') }}
        </button>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-16">
      <div class="w-24 h-24 bg-secondary rounded-full flex items-center justify-center mx-auto mb-6">
        <svg class="w-12 h-12 text-tertiary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      </div>
      <h3 class="text-xl font-semibold text-primary mb-2">
        {{ $t('home.noFeedback') }}
      </h3>
      <p class="text-secondary mb-6">
        {{ $t('home.description') }}
      </p>
      <NuxtLink to="/submit" class="btn btn-primary">
        {{ $t('nav.submit') }}
      </NuxtLink>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import FeedbackCard from '~/components/FeedbackCard.vue'

const config = useRuntimeConfig()

// Reactive data
const feedbackList = ref([])
const isLoading = ref(false)
const hasMore = ref(true)
const sortBy = ref('newest')

// Mock data for demonstration
const mockFeedback = [
  {
    id: 1,
    title: "Add dark mode support",
    content: "It would be great to have a dark mode option for better user experience during night time usage.",
    author: "John Doe",
    votes: 15,
    userVoted: false,
    createdAt: "2024-01-15T10:30:00Z"
  },
  {
    id: 2,
    title: "Improve mobile responsiveness",
    content: "The current mobile layout could be improved. Some buttons are too small and text is hard to read on smaller screens.",
    author: "Jane Smith",
    votes: 8,
    userVoted: true,
    createdAt: "2024-01-14T15:45:00Z"
  },
  {
    id: 3,
    title: "Add search functionality",
    content: "Users should be able to search through existing feedback to avoid duplicates and find relevant suggestions.",
    author: "Mike Johnson",
    votes: 23,
    userVoted: false,
    createdAt: "2024-01-13T09:20:00Z"
  }
]

// Computed properties
const sortedFeedback = computed(() => {
  const sorted = [...feedbackList.value]

  switch (sortBy.value) {
    case 'newest':
      return sorted.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    case 'oldest':
      return sorted.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
    case 'mostVoted':
      return sorted.sort((a, b) => b.votes - a.votes)
    default:
      return sorted
  }
})

// Methods
const loadFeedback = async () => {
  isLoading.value = true

  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    feedbackList.value = mockFeedback
    hasMore.value = false
  } catch (error) {
    console.error('Error loading feedback:', error)
  } finally {
    isLoading.value = false
  }
}

const loadMore = async () => {
  // Implement load more functionality
  console.log('Load more feedback')
}

const handleVote = async (voteData) => {
  try {
    // Find the feedback item and update vote
    const feedback = feedbackList.value.find(f => f.id === voteData.feedbackId)
    if (feedback) {
      if (voteData.voted && !feedback.userVoted) {
        feedback.votes += 1
        feedback.userVoted = true
      } else if (!voteData.voted && feedback.userVoted) {
        feedback.votes -= 1
        feedback.userVoted = false
      }
    }

    // Here you would make an API call to update the vote on the server
    console.log('Vote updated:', voteData)
  } catch (error) {
    console.error('Error updating vote:', error)
  }
}

// Lifecycle
onMounted(() => {
  loadFeedback()
})
</script>
