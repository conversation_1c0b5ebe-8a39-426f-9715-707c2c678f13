# Voice.new - User Feedback Collection Platform

A modern, multilingual user feedback collection platform built with Nuxt 3, featuring upvoting, user authentication, and a clean, accessible UI.

## Features

### 🌍 Multilingual Support
- Default English with support for 7 languages
- Chinese (Simplified), Spanish, French, German, Japanese, Korean
- Automatic browser language detection
- Language switching with persistent preferences

### 🔐 User Authentication
- User registration and login
- JWT-based authentication
- Protected routes for feedback submission
- User profile management

### 📝 Feedback Management
- Submit detailed feedback with title and description
- Vote on feedback submissions (upvote system)
- Sort feedback by newest, oldest, or most voted
- Pagination for large feedback lists
- Individual feedback detail pages

### 🎨 Modern UI/UX
- Clean, minimalist design without gradients or shadows
- Light and dark mode support with system preference detection
- Fully responsive design for all device sizes
- Accessible components with proper ARIA labels
- Smooth animations and transitions

### 🚀 SEO Optimized
- Server-side rendering (SSR)
- Dynamic meta tags for each page
- Canonical URLs for all pages
- Automatic sitemap generation
- Structured data markup
- Optimized robots.txt

## Tech Stack

- **Framework**: Nuxt 3
- **Styling**: Tailwind CSS 4
- **Internationalization**: @nuxtjs/i18n
- **Theme**: @nuxtjs/color-mode
- **Authentication**: JWT with bcryptjs
- **Language**: TypeScript
- **Package Manager**: pnpm

## Getting Started

### Prerequisites
- Node.js 18+ 
- pnpm (recommended)

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd voice.new
```

2. Install dependencies:
```bash
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. Start the development server:
```bash
pnpm dev
```

The application will be available at `http://localhost:3000` (or the next available port).

### Environment Variables

```env
JWT_SECRET=your-super-secret-jwt-key
NODE_ENV=development
```

## Project Structure

```
voice.new/
├── assets/css/           # Global styles and Tailwind CSS
├── components/           # Reusable Vue components
├── composables/          # Vue composables for state management
├── i18n/locales/         # Translation files
├── layouts/              # Nuxt layouts
├── middleware/           # Route middleware
├── pages/                # Application pages (auto-routed)
├── plugins/              # Nuxt plugins
├── public/               # Static assets
├── server/api/           # Server-side API routes
└── types/                # TypeScript type definitions
```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### Feedback
- `GET /api/feedback` - Get feedback list (with pagination and sorting)
- `POST /api/feedback` - Create new feedback
- `GET /api/feedback/[id]` - Get specific feedback
- `POST /api/feedback/[id]/vote` - Vote on feedback

### Utilities
- `GET /api/sitemap.xml` - Generate sitemap

## Development

### Adding New Languages

1. Create a new translation file in `i18n/locales/`:
```bash
cp i18n/locales/en.json i18n/locales/[locale].json
```

2. Translate all keys in the new file

3. Add the locale to `i18n/locales/index.js`:
```javascript
{
  code: 'locale-code',
  language: 'locale-language',
  file: 'locale.json',
  name: 'Language Name'
}
```

### Customizing Styles

The project uses Tailwind CSS with custom CSS variables for theming. Main style configurations are in:
- `assets/css/main.css` - Custom styles and theme variables
- `nuxt.config.ts` - Tailwind and color mode configuration

### Database Integration

Currently uses mock data. To integrate with a real database:

1. Choose your database (PostgreSQL, MySQL, MongoDB, etc.)
2. Set up database connection in `server/api/` routes
3. Replace mock data with actual database queries
4. Add proper error handling and validation

## Deployment

### Build for Production

```bash
pnpm build
```

### Preview Production Build

```bash
pnpm preview
```

### Deploy to Vercel/Netlify

The project is ready for deployment to modern hosting platforms:

1. Connect your repository to your hosting platform
2. Set environment variables
3. Deploy with automatic builds

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/amazing-feature`
3. Commit your changes: `git commit -m 'Add amazing feature'`
4. Push to the branch: `git push origin feature/amazing-feature`
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please open an issue in the GitHub repository or contact the development team.
