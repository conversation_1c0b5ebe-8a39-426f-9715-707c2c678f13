export default defineEventHandler(async (event) => {
  const baseUrl = 'https://voice.new'
  
  // Static pages
  const staticPages = [
    { url: '/', changefreq: 'daily', priority: 1.0 },
    { url: '/submit', changefreq: 'monthly', priority: 0.8 },
    { url: '/login', changefreq: 'monthly', priority: 0.5 },
    { url: '/register', changefreq: 'monthly', priority: 0.5 }
  ]
  
  // Dynamic feedback pages (in a real app, fetch from database)
  const feedbackPages = [
    { url: '/feedback/1', changefreq: 'weekly', priority: 0.7 },
    { url: '/feedback/2', changefreq: 'weekly', priority: 0.7 },
    { url: '/feedback/3', changefreq: 'weekly', priority: 0.7 }
  ]
  
  const allPages = [...staticPages, ...feedbackPages]
  
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${allPages.map(page => `  <url>
    <loc>${baseUrl}${page.url}</loc>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
  </url>`).join('\n')}
</urlset>`
  
  setHeader(event, 'Content-Type', 'application/xml')
  return sitemap
})
