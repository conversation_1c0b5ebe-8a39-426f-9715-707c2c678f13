// Create new feedback
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    
    // Validate required fields
    if (!body.title || !body.content || !body.category || !body.userId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Title, content, category, and userId are required'
      })
    }
    
    // Validate field lengths
    if (body.title.length < 5 || body.title.length > 100) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Title must be between 5 and 100 characters'
      })
    }
    
    if (body.content.length < 10 || body.content.length > 1000) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Content must be between 10 and 1000 characters'
      })
    }
    
    // Validate category
    const validCategories = ['feature', 'bug', 'improvement', 'other']
    if (!validCategories.includes(body.category)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid category'
      })
    }

    // Validate email format if contact is provided
    if (body.contact && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(body.contact)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid email format'
      })
    }

    // TODO: Get user from JWT token
    const authorId = Date.now() // Mock author ID
    const userName = "Anonymous User" // Mock user name

    // Create new feedback object
    const newFeedback = {
      id: Date.now(), // Mock ID generation
      title: body.title.trim(),
      content: body.content.trim(),
      category: body.category,
      contact: body.contact?.trim() || undefined,
      author: userName,
      authorId: authorId,
      userId: body.userId,
      votes: 0,
      userVoted: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    // TODO: Save to database
    console.log('New feedback created:', newFeedback)
    
    return {
      success: true,
      data: newFeedback,
      message: 'Feedback created successfully'
    }
    
  } catch (error) {
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
