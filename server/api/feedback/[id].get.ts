// Get feedback by ID
export default defineEventHandler(async (event) => {
  const feedbackId = parseInt(getRouterParam(event, 'id') as string)
  
  if (!feedbackId || isNaN(feedbackId)) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid feedback ID'
    })
  }
  
  // Mock data - replace with actual database query
  const mockFeedback = {
    1: {
      id: 1,
      title: "Add dark mode support",
      content: "It would be great to have a dark mode option for better user experience during night time usage. This feature would help reduce eye strain and provide a more comfortable viewing experience in low-light environments.\n\nMany modern applications now include dark mode as a standard feature, and users have come to expect this functionality. The implementation could include:\n\n1. A toggle switch in the header\n2. System preference detection\n3. Persistent user preference storage\n4. Smooth transitions between themes\n\nThis would significantly improve the overall user experience and accessibility of the platform.",
      author: "<PERSON>",
      authorId: 1,
      votes: 15,
      userVoted: false,
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z"
    },
    2: {
      id: 2,
      title: "Improve mobile responsiveness",
      content: "The current mobile layout could be improved. Some buttons are too small and text is hard to read on smaller screens. Here are some specific issues I've noticed:\n\n1. Navigation buttons are too small on mobile devices\n2. Text size is difficult to read on screens smaller than 375px\n3. Form inputs don't scale properly\n4. The feedback cards overlap on very small screens\n\nSuggested improvements:\n- Increase minimum touch target size to 44px\n- Implement responsive typography\n- Add better spacing between elements\n- Test on various device sizes",
      author: "Jane Smith",
      authorId: 2,
      votes: 8,
      userVoted: true,
      createdAt: "2024-01-14T15:45:00Z",
      updatedAt: "2024-01-14T15:45:00Z"
    },
    3: {
      id: 3,
      title: "Add search functionality",
      content: "Users should be able to search through existing feedback to avoid duplicates and find relevant suggestions. This would include:\n\n1. Full-text search across titles and content\n2. Filter by date range\n3. Filter by vote count\n4. Sort search results by relevance\n\nThis feature would greatly improve the user experience and help prevent duplicate feedback submissions.",
      author: "Mike Johnson",
      authorId: 3,
      votes: 23,
      userVoted: false,
      createdAt: "2024-01-13T09:20:00Z",
      updatedAt: "2024-01-13T09:20:00Z"
    }
  }
  
  const feedback = mockFeedback[feedbackId as keyof typeof mockFeedback]
  
  if (!feedback) {
    throw createError({
      statusCode: 404,
      statusMessage: 'Feedback not found'
    })
  }
  
  return {
    success: true,
    data: feedback
  }
})
