// Get all feedback with pagination and sorting
export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const page = parseInt(query.page as string) || 1
  const limit = parseInt(query.limit as string) || 10
  const sortBy = query.sortBy as string || 'newest'
  const userId = query.userId as string
  const status = query.status as string
  const category = query.category as string
  const priority = query.priority as string
  
  // Mock data - replace with actual database queries
  const mockFeedback = [
    {
      id: 1,
      title: "Add dark mode support",
      content: "It would be great to have a dark mode option for better user experience during night time usage.",
      category: "feature",
      contact: "<EMAIL>",
      author: "<PERSON>",
      authorId: 1,
      userId: "demo",
      votes: 15,
      userVoted: false,
      tags: [
        { id: 1, name: "UI/UX", color: "#8B5CF6", description: "User interface and experience" },
        { id: 2, name: "High Priority", color: "#EF4444", description: "High priority feature" }
      ],
      status: "planned",
      priority: "high",
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z"
    },
    {
      id: 2,
      title: "Improve mobile responsiveness",
      content: "The current mobile layout could be improved. Some buttons are too small and text is hard to read on smaller screens.",
      category: "improvement",
      contact: "<EMAIL>",
      author: "Jane Smith",
      authorId: 2,
      userId: "demo",
      votes: 8,
      userVoted: true,
      tags: [
        { id: 3, name: "Mobile", color: "#10B981", description: "Mobile-related improvements" },
        { id: 1, name: "UI/UX", color: "#8B5CF6", description: "User interface and experience" }
      ],
      status: "in-progress",
      priority: "medium",
      createdAt: "2024-01-14T15:45:00Z",
      updatedAt: "2024-01-14T15:45:00Z"
    },
    {
      id: 3,
      title: "Add search functionality",
      content: "Users should be able to search through existing feedback to avoid duplicates and find relevant suggestions.",
      category: "feature",
      author: "Mike Johnson",
      authorId: 3,
      userId: "john",
      votes: 23,
      userVoted: false,
      tags: [
        { id: 4, name: "Search", color: "#F59E0B", description: "Search and filtering features" },
        { id: 2, name: "High Priority", color: "#EF4444", description: "High priority feature" }
      ],
      status: "under-review",
      priority: "high",
      createdAt: "2024-01-13T09:20:00Z",
      updatedAt: "2024-01-13T09:20:00Z"
    },
    {
      id: 4,
      title: "Export feedback to CSV",
      content: "It would be helpful to export all feedback data to CSV format for analysis and reporting purposes.",
      category: "feature",
      author: "Sarah Wilson",
      authorId: 4,
      userId: "jane",
      votes: 12,
      userVoted: false,
      tags: [
        { id: 5, name: "Export", color: "#6366F1", description: "Data export features" },
        { id: 6, name: "Analytics", color: "#EC4899", description: "Analytics and reporting" }
      ],
      status: "open",
      priority: "medium",
      createdAt: "2024-01-12T14:20:00Z",
      updatedAt: "2024-01-12T14:20:00Z"
    },
    {
      id: 5,
      title: "Add email notifications",
      content: "Users should receive email notifications when their feedback receives votes or comments.",
      category: "feature",
      contact: "<EMAIL>",
      author: "David Brown",
      authorId: 5,
      userId: "demo",
      votes: 7,
      userVoted: false,
      tags: [
        { id: 7, name: "Notifications", color: "#14B8A6", description: "Email and push notifications" }
      ],
      status: "completed",
      priority: "low",
      createdAt: "2024-01-11T09:15:00Z",
      updatedAt: "2024-01-11T09:15:00Z"
    }
  ]
  
  // Apply filters
  let filteredFeedback = mockFeedback

  // Filter by user if userId is provided
  if (userId) {
    filteredFeedback = filteredFeedback.filter(feedback => feedback.userId === userId)
  }

  // Filter by status if provided
  if (status) {
    filteredFeedback = filteredFeedback.filter(feedback => feedback.status === status)
  }

  // Filter by category if provided
  if (category) {
    filteredFeedback = filteredFeedback.filter(feedback => feedback.category === category)
  }

  // Filter by priority if provided
  if (priority) {
    filteredFeedback = filteredFeedback.filter(feedback => feedback.priority === priority)
  }

  // Sort feedback
  let sortedFeedback = [...filteredFeedback]
  switch (sortBy) {
    case 'newest':
      sortedFeedback.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      break
    case 'oldest':
      sortedFeedback.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
      break
    case 'mostVoted':
      sortedFeedback.sort((a, b) => b.votes - a.votes)
      break
  }
  
  // Pagination
  const startIndex = (page - 1) * limit
  const endIndex = startIndex + limit
  const paginatedFeedback = sortedFeedback.slice(startIndex, endIndex)

  const totalCount = filteredFeedback.length
  const totalPages = Math.ceil(totalCount / limit)
  const hasMore = page < totalPages
  
  return {
    success: true,
    data: paginatedFeedback,
    pagination: {
      page,
      limit,
      totalCount,
      totalPages,
      hasMore
    }
  }
})
