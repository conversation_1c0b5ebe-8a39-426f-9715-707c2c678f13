// Get all feedback with pagination and sorting
export default defineEventHandler(async (event) => {
  const query = getQuery(event)
  const page = parseInt(query.page as string) || 1
  const limit = parseInt(query.limit as string) || 10
  const sortBy = query.sortBy as string || 'newest'
  
  // Mock data - replace with actual database queries
  const mockFeedback = [
    {
      id: 1,
      title: "Add dark mode support",
      content: "It would be great to have a dark mode option for better user experience during night time usage.",
      author: "<PERSON>",
      authorId: 1,
      votes: 15,
      userVoted: false,
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-15T10:30:00Z"
    },
    {
      id: 2,
      title: "Improve mobile responsiveness",
      content: "The current mobile layout could be improved. Some buttons are too small and text is hard to read on smaller screens.",
      author: "<PERSON>",
      authorId: 2,
      votes: 8,
      userVoted: true,
      createdAt: "2024-01-14T15:45:00Z",
      updatedAt: "2024-01-14T15:45:00Z"
    },
    {
      id: 3,
      title: "Add search functionality",
      content: "Users should be able to search through existing feedback to avoid duplicates and find relevant suggestions.",
      author: "<PERSON>",
      authorId: 3,
      votes: 23,
      userVoted: false,
      createdAt: "2024-01-13T09:20:00Z",
      updatedAt: "2024-01-13T09:20:00Z"
    },
    {
      id: 4,
      title: "Export feedback to CSV",
      content: "It would be helpful to export all feedback data to CSV format for analysis and reporting purposes.",
      author: "Sarah Wilson",
      authorId: 4,
      votes: 12,
      userVoted: false,
      createdAt: "2024-01-12T14:20:00Z",
      updatedAt: "2024-01-12T14:20:00Z"
    },
    {
      id: 5,
      title: "Add email notifications",
      content: "Users should receive email notifications when their feedback receives votes or comments.",
      author: "David Brown",
      authorId: 5,
      votes: 7,
      userVoted: false,
      createdAt: "2024-01-11T09:15:00Z",
      updatedAt: "2024-01-11T09:15:00Z"
    }
  ]
  
  // Sort feedback
  let sortedFeedback = [...mockFeedback]
  switch (sortBy) {
    case 'newest':
      sortedFeedback.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
      break
    case 'oldest':
      sortedFeedback.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
      break
    case 'mostVoted':
      sortedFeedback.sort((a, b) => b.votes - a.votes)
      break
  }
  
  // Pagination
  const startIndex = (page - 1) * limit
  const endIndex = startIndex + limit
  const paginatedFeedback = sortedFeedback.slice(startIndex, endIndex)
  
  const totalCount = mockFeedback.length
  const totalPages = Math.ceil(totalCount / limit)
  const hasMore = page < totalPages
  
  return {
    success: true,
    data: paginatedFeedback,
    pagination: {
      page,
      limit,
      totalCount,
      totalPages,
      hasMore
    }
  }
})
