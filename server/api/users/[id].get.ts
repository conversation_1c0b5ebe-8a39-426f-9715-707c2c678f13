export default defineEventHandler(async (event) => {
  const userId = getRouterParam(event, 'id')
  
  if (!userId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'User ID is required'
    })
  }

  // Mock user data - in a real app, this would come from a database
  const mockUsers = {
    'demo': {
      id: 'demo',
      name: 'Demo User',
      email: '<EMAIL>',
      createdAt: '2024-01-01T00:00:00Z'
    },
    'john': {
      id: 'john',
      name: '<PERSON>',
      email: '<EMAIL>',
      createdAt: '2024-01-15T00:00:00Z'
    },
    'jane': {
      id: 'jane',
      name: '<PERSON>',
      email: '<EMAIL>',
      createdAt: '2024-02-01T00:00:00Z'
    }
  }

  const user = mockUsers[userId as keyof typeof mockUsers]
  
  if (!user) {
    throw createError({
      statusCode: 404,
      statusMessage: 'User not found'
    })
  }

  return {
    success: true,
    data: user
  }
})
