import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

// User login
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const config = useRuntimeConfig()
    
    // Validate required fields
    if (!body.email || !body.password) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Email and password are required'
      })
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid email format'
      })
    }
    
    // Mock user database - replace with actual database query
    const mockUsers = [
      {
        id: 1,
        name: '<PERSON>',
        email: '<EMAIL>',
        password: await bcrypt.hash('password123', 10), // hashed password
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '<PERSON>',
        email: '<EMAIL>',
        password: await bcrypt.hash('password456', 10),
        createdAt: '2024-01-02T00:00:00Z'
      }
    ]
    
    // Find user by email
    const user = mockUsers.find(u => u.email.toLowerCase() === body.email.toLowerCase())
    
    if (!user) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid email or password'
      })
    }
    
    // Verify password
    const isValidPassword = await bcrypt.compare(body.password, user.password)
    
    if (!isValidPassword) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid email or password'
      })
    }
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: user.id,
        email: user.email,
        name: user.name
      },
      config.jwtSecret,
      { expiresIn: '7d' }
    )
    
    // Set HTTP-only cookie
    setCookie(event, 'auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    })
    
    // Return user data (without password)
    const { password: _, ...userWithoutPassword } = user
    
    return {
      success: true,
      data: {
        user: userWithoutPassword,
        token
      },
      message: 'Login successful'
    }
    
  } catch (error) {
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
