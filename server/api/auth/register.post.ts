import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

// User registration
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const config = useRuntimeConfig()
    
    // Validate required fields
    if (!body.name || !body.email || !body.password) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Name, email, and password are required'
      })
    }
    
    // Validate field lengths
    if (body.name.length < 2 || body.name.length > 50) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Name must be between 2 and 50 characters'
      })
    }
    
    if (body.password.length < 6) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Password must be at least 6 characters'
      })
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(body.email)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid email format'
      })
    }
    
    // Mock user database - replace with actual database query
    const mockUsers = [
      {
        id: 1,
        name: '<PERSON>',
        email: '<EMAIL>',
        password: 'hashedpassword1',
        createdAt: '2024-01-01T00:00:00<PERSON>'
      },
      {
        id: 2,
        name: 'Jane <PERSON>',
        email: '<EMAIL>',
        password: 'hashedpassword2',
        createdAt: '2024-01-02T00:00:00Z'
      }
    ]
    
    // Check if user already exists
    const existingUser = mockUsers.find(u => u.email.toLowerCase() === body.email.toLowerCase())
    
    if (existingUser) {
      throw createError({
        statusCode: 409,
        statusMessage: 'User with this email already exists'
      })
    }
    
    // Hash password
    const hashedPassword = await bcrypt.hash(body.password, 10)
    
    // Create new user
    const newUser = {
      id: Date.now(), // Mock ID generation
      name: body.name.trim(),
      email: body.email.toLowerCase().trim(),
      password: hashedPassword,
      createdAt: new Date().toISOString()
    }
    
    // TODO: Save user to database
    console.log('New user created:', { ...newUser, password: '[HIDDEN]' })
    
    // Generate JWT token
    const token = jwt.sign(
      { 
        userId: newUser.id,
        email: newUser.email,
        name: newUser.name
      },
      config.jwtSecret,
      { expiresIn: '7d' }
    )
    
    // Set HTTP-only cookie
    setCookie(event, 'auth-token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60 * 24 * 7 // 7 days
    })
    
    // Return user data (without password)
    const { password: _, ...userWithoutPassword } = newUser
    
    return {
      success: true,
      data: {
        user: userWithoutPassword,
        token
      },
      message: 'Registration successful'
    }
    
  } catch (error) {
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
