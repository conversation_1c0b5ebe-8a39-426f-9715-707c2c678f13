import jwt from 'jsonwebtoken'

// Get current user information
export default defineEventHandler(async (event) => {
  try {
    const config = useRuntimeConfig()
    
    // Get token from cookie
    const token = getCookie(event, 'auth-token')
    
    if (!token) {
      throw createError({
        statusCode: 401,
        statusMessage: 'No authentication token provided'
      })
    }
    
    // Verify JWT token
    let decoded
    try {
      decoded = jwt.verify(token, config.jwtSecret) as any
    } catch (jwtError) {
      throw createError({
        statusCode: 401,
        statusMessage: 'Invalid or expired token'
      })
    }
    
    // Mock user database - replace with actual database query
    const mockUsers = [
      {
        id: 1,
        name: '<PERSON>',
        email: '<EMAIL>',
        createdAt: '2024-01-01T00:00:00Z'
      },
      {
        id: 2,
        name: '<PERSON>',
        email: '<EMAIL>',
        createdAt: '2024-01-02T00:00:00Z'
      }
    ]
    
    // Find user by ID from token
    const user = mockUsers.find(u => u.id === decoded.userId)
    
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      })
    }
    
    return {
      success: true,
      data: {
        user
      }
    }
    
  } catch (error) {
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: 'Internal server error'
    })
  }
})
