@import "tailwindcss";

/* Custom CSS Variables for Theme - Naive Blue Style */
:root {
  --color-primary: #2080f0;
  --color-primary-hover: #1c7ed6;
  --color-primary-light: #e7f5ff;
  --color-secondary: #64748b;
  --color-success: #18a058;
  --color-error: #d03050;
  --color-warning: #f0a020;

  /* Light theme */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #fafbfc;
  --color-bg-tertiary: #f5f7fa;
  --color-text-primary: #1a1a1a;
  --color-text-secondary: #606266;
  --color-text-tertiary: #909399;
  --color-border: #e4e7ed;
  --color-border-hover: #c0c4cc;
}

/* Dark theme */
.dark {
  --color-bg-primary: #101014;
  --color-bg-secondary: #16161a;
  --color-bg-tertiary: #1c1c23;
  --color-text-primary: #f2f2f3;
  --color-text-secondary: #c2c2c4;
  --color-text-tertiary: #a1a1a3;
  --color-border: #262626;
  --color-border-hover: #3c3c41;
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Inter', 'Segoe UI', Roboto, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom button styles */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  border-radius: 0;
  font-weight: 500;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border: 1px solid var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.btn-primary:focus {
  --tw-ring-color: var(--color-primary);
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-bg-secondary);
  border-color: var(--color-border-hover);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-text-secondary);
  border: none;
}

.btn-ghost:hover {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

/* Custom input styles */
.input {
  @apply w-full px-3 py-2 text-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  border-radius: 0;
  font-weight: 400;
}

.input:hover {
  border-color: var(--color-border-hover);
}

.input:focus {
  border-color: var(--color-primary);
  --tw-ring-color: var(--color-primary);
}

.input::placeholder {
  color: var(--color-text-tertiary);
}

/* Custom card styles */
.card {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: 0;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.card:hover {
  border-color: var(--color-border-hover);
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  transform: translateY(-1px);
  border-color: var(--color-primary);
}

/* Vote button styles */
.vote-btn {
  @apply flex flex-col items-center justify-center p-2 transition-all duration-200;
  background-color: var(--color-bg-secondary);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  border-radius: 0;
  min-width: 3rem;
}

.vote-btn:hover {
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
  border-color: var(--color-border-hover);
}

.vote-btn.voted {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.vote-btn.voted:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

/* Navigation styles */
.nav-link {
  @apply px-3 py-2 text-sm font-medium transition-all duration-200;
  color: var(--color-text-secondary);
  border-radius: 0;
}

.nav-link:hover {
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
}

.nav-link.active {
  color: var(--color-primary);
  background-color: var(--color-bg-secondary);
}

/* Utility classes */
.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-tertiary {
  color: var(--color-text-tertiary);
}

.bg-primary {
  background-color: var(--color-bg-primary);
}

.bg-secondary {
  background-color: var(--color-bg-secondary);
}

.bg-tertiary {
  background-color: var(--color-bg-tertiary);
}

.border-color {
  border-color: var(--color-border);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Blue accent styles */
.blue-accent {
  background-color: var(--color-primary);
  color: white;
}

.blue-accent-light {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
}

.hero-section {
  background: linear-gradient(135deg, var(--color-primary) 0%, var(--color-primary-hover) 100%);
  color: white;
}

.container-narrow {
  max-width: 800px;
  margin: 0 auto;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, var(--color-bg-secondary) 25%, var(--color-bg-tertiary) 50%, var(--color-bg-secondary) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Enhanced card interactions */
.card-interactive:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Status indicators */
.status-indicator {
  position: relative;
}

.status-indicator::before {
  content: '';
  position: absolute;
  left: -8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 100%;
  border-radius: 2px;
}

/* Filter section styling */
.filter-section {
  background: var(--color-bg-secondary);
  border: 1px solid var(--color-border);
  transition: all 0.2s ease;
}

.filter-section:hover {
  border-color: var(--color-border-hover);
}

/* Responsive utilities */
@media (max-width: 640px) {
  .card {
    padding: 1rem;
  }

  .btn {
    @apply px-3 py-1.5 text-xs;
  }

  .container-narrow {
    max-width: 100%;
    padding: 0 1rem;
  }

  .filter-section {
    flex-direction: column;
    gap: 1rem;
  }

  .filter-section > div {
    flex-direction: column;
    align-items: flex-start !important;
    gap: 0.5rem;
  }
}