@import "tailwindcss";

/* Custom CSS Variables for Theme */
:root {
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-secondary: #64748b;
  --color-success: #059669;
  --color-error: #dc2626;
  --color-warning: #d97706;

  /* Light theme */
  --color-bg-primary: #ffffff;
  --color-bg-secondary: #f8fafc;
  --color-bg-tertiary: #f1f5f9;
  --color-text-primary: #0f172a;
  --color-text-secondary: #475569;
  --color-text-tertiary: #64748b;
  --color-border: #e2e8f0;
  --color-border-hover: #cbd5e1;
}

/* Dark theme */
.dark {
  --color-bg-primary: #0f172a;
  --color-bg-secondary: #1e293b;
  --color-bg-tertiary: #334155;
  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-tertiary: #94a3b8;
  --color-border: #334155;
  --color-border-hover: #475569;
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Custom button styles */
.btn {
  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
}

.btn-primary {
  background-color: var(--color-primary);
  color: white;
  border: 1px solid var(--color-primary);
}

.btn-primary:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.btn-primary:focus {
  --tw-ring-color: var(--color-primary);
}

.btn-lg {
  @apply px-6 py-3 text-base;
}

.btn-secondary {
  background-color: transparent;
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.btn-secondary:hover {
  background-color: var(--color-bg-secondary);
  border-color: var(--color-border-hover);
}

.btn-ghost {
  background-color: transparent;
  color: var(--color-text-secondary);
  border: none;
}

.btn-ghost:hover {
  background-color: var(--color-bg-secondary);
  color: var(--color-text-primary);
}

/* Custom input styles */
.input {
  @apply w-full px-3 py-2 text-sm rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  background-color: var(--color-bg-primary);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
}

.input:hover {
  border-color: var(--color-border-hover);
}

.input:focus {
  border-color: var(--color-primary);
  --tw-ring-color: var(--color-primary);
}

.input::placeholder {
  color: var(--color-text-tertiary);
}

/* Custom card styles */
.card {
  background-color: var(--color-bg-primary);
  border: 1px solid var(--color-border);
  border-radius: 0.75rem;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.card:hover {
  border-color: var(--color-border-hover);
}

.card-interactive {
  cursor: pointer;
}

.card-interactive:hover {
  transform: translateY(-1px);
  border-color: var(--color-primary);
}

/* Vote button styles */
.vote-btn {
  @apply flex flex-col items-center justify-center p-2 rounded-lg transition-all duration-200;
  background-color: var(--color-bg-secondary);
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  min-width: 3rem;
}

.vote-btn:hover {
  background-color: var(--color-bg-tertiary);
  color: var(--color-text-primary);
  border-color: var(--color-border-hover);
}

.vote-btn.voted {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.vote-btn.voted:hover {
  background-color: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

/* Navigation styles */
.nav-link {
  @apply px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200;
  color: var(--color-text-secondary);
}

.nav-link:hover {
  color: var(--color-text-primary);
  background-color: var(--color-bg-secondary);
}

.nav-link.active {
  color: var(--color-primary);
  background-color: var(--color-bg-secondary);
}

/* Utility classes */
.text-primary {
  color: var(--color-primary);
}

.text-secondary {
  color: var(--color-text-secondary);
}

.text-tertiary {
  color: var(--color-text-tertiary);
}

.bg-primary {
  background-color: var(--color-bg-primary);
}

.bg-secondary {
  background-color: var(--color-bg-secondary);
}

.bg-tertiary {
  background-color: var(--color-bg-tertiary);
}

.border-color {
  border-color: var(--color-border);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Responsive utilities */
@media (max-width: 640px) {
  .card {
    padding: 1rem;
  }

  .btn {
    @apply px-3 py-1.5 text-xs;
  }
}