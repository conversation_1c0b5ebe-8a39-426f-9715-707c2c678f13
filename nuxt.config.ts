import tailwindcss from "@tailwindcss/vite";
import locales from './i18n/locales'

export default defineNuxtConfig({
  compatibilityDate: '2025-05-15',
  devtools: {
    enabled: true
  },
  css: [
    '~/assets/css/main.css'
  ],
  modules: [
    '@nuxtjs/i18n'
  ],
  vite: {
    plugins: [
      tailwindcss(),
    ],
  },
  i18n: {
    defaultLocale: 'en',
    locales,
    strategy: 'prefix_except_default',
    baseUrl: 'https://query.domains/'
  }
})
