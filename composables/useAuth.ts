interface User {
  id: number
  name: string
  email: string
  createdAt: string
}

interface AuthState {
  user: User | null
  isLoggedIn: boolean
  isLoading: boolean
}

// Global auth state
const authState = reactive<AuthState>({
  user: null,
  isLoggedIn: false,
  isLoading: true
})

export const useAuth = () => {
  // Login function
  const login = async (email: string, password: string) => {
    try {
      const { data } = await $fetch('/api/auth/login', {
        method: 'POST',
        body: { email, password }
      })
      
      authState.user = data.user
      authState.isLoggedIn = true
      
      return { success: true, data }
    } catch (error: any) {
      console.error('Login error:', error)
      return { 
        success: false, 
        error: error.data?.message || 'Login failed' 
      }
    }
  }

  // Register function
  const register = async (name: string, email: string, password: string) => {
    try {
      const { data } = await $fetch('/api/auth/register', {
        method: 'POST',
        body: { name, email, password }
      })
      
      authState.user = data.user
      authState.isLoggedIn = true
      
      return { success: true, data }
    } catch (error: any) {
      console.error('Registration error:', error)
      return { 
        success: false, 
        error: error.data?.message || 'Registration failed' 
      }
    }
  }

  // Logout function
  const logout = async () => {
    try {
      await $fetch('/api/auth/logout', {
        method: 'POST'
      })
      
      authState.user = null
      authState.isLoggedIn = false
      
      // Redirect to home page
      await navigateTo('/')
      
      return { success: true }
    } catch (error: any) {
      console.error('Logout error:', error)
      return { 
        success: false, 
        error: error.data?.message || 'Logout failed' 
      }
    }
  }

  // Check authentication status
  const checkAuth = async () => {
    authState.isLoading = true
    
    try {
      const { data } = await $fetch('/api/auth/me')
      
      authState.user = data.user
      authState.isLoggedIn = true
    } catch (error) {
      authState.user = null
      authState.isLoggedIn = false
    } finally {
      authState.isLoading = false
    }
  }

  // Initialize auth state on app start
  const initAuth = async () => {
    if (process.client) {
      await checkAuth()
    }
  }

  return {
    // State
    user: readonly(toRef(authState, 'user')),
    isLoggedIn: readonly(toRef(authState, 'isLoggedIn')),
    isLoading: readonly(toRef(authState, 'isLoading')),
    
    // Actions
    login,
    register,
    logout,
    checkAuth,
    initAuth
  }
}
