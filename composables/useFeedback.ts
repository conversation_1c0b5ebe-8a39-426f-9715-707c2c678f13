interface Feedback {
  id: number
  title: string
  content: string
  author: string
  authorId: number
  votes: number
  userVoted: boolean
  createdAt: string
  updatedAt: string
}

interface FeedbackListResponse {
  success: boolean
  data: Feedback[]
  pagination: {
    page: number
    limit: number
    totalCount: number
    totalPages: number
    hasMore: boolean
  }
}

interface FeedbackResponse {
  success: boolean
  data: Feedback
}

export const useFeedback = () => {
  // Get all feedback with pagination and sorting
  const getFeedbackList = async (options: {
    page?: number
    limit?: number
    sortBy?: 'newest' | 'oldest' | 'mostVoted'
  } = {}) => {
    try {
      const query = new URLSearchParams()
      
      if (options.page) query.append('page', options.page.toString())
      if (options.limit) query.append('limit', options.limit.toString())
      if (options.sortBy) query.append('sortBy', options.sortBy)
      
      const response: FeedbackListResponse = await $fetch(`/api/feedback?${query.toString()}`)
      
      return { success: true, data: response }
    } catch (error: any) {
      console.error('Error fetching feedback list:', error)
      return { 
        success: false, 
        error: error.data?.message || 'Failed to fetch feedback' 
      }
    }
  }

  // Get single feedback by ID
  const getFeedback = async (id: number) => {
    try {
      const response: FeedbackResponse = await $fetch(`/api/feedback/${id}`)
      
      return { success: true, data: response.data }
    } catch (error: any) {
      console.error('Error fetching feedback:', error)
      return { 
        success: false, 
        error: error.data?.message || 'Failed to fetch feedback' 
      }
    }
  }

  // Create new feedback
  const createFeedback = async (title: string, content: string) => {
    try {
      const response = await $fetch('/api/feedback', {
        method: 'POST',
        body: { title, content }
      })
      
      return { success: true, data: response.data }
    } catch (error: any) {
      console.error('Error creating feedback:', error)
      return { 
        success: false, 
        error: error.data?.message || 'Failed to create feedback' 
      }
    }
  }

  // Vote on feedback
  const voteFeedback = async (feedbackId: number, voted: boolean) => {
    try {
      const response = await $fetch(`/api/feedback/${feedbackId}/vote`, {
        method: 'POST',
        body: { voted }
      })
      
      return { success: true, data: response.data }
    } catch (error: any) {
      console.error('Error voting on feedback:', error)
      return { 
        success: false, 
        error: error.data?.message || 'Failed to vote on feedback' 
      }
    }
  }

  return {
    getFeedbackList,
    getFeedback,
    createFeedback,
    voteFeedback
  }
}
