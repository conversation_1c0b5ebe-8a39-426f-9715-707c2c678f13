interface Tag {
  id: number
  name: string
  color: string
  description?: string
}

interface Feedback {
  id: number
  title: string
  content: string
  category: string
  contact?: string
  author: string
  authorId: number
  userId: string
  votes: number
  userVoted: boolean
  tags: Tag[]
  status: 'open' | 'under-review' | 'planned' | 'in-progress' | 'completed' | 'declined'
  priority: 'low' | 'medium' | 'high' | 'critical'
  createdAt: string
  updatedAt: string
}

interface User {
  id: string
  name: string
  email?: string
  createdAt: string
}

interface FeedbackListResponse {
  success: boolean
  data: Feedback[]
  pagination: {
    page: number
    limit: number
    totalCount: number
    totalPages: number
    hasMore: boolean
  }
}

interface FeedbackResponse {
  success: boolean
  data: Feedback
}

interface UserResponse {
  success: boolean
  data: User
}

export const useFeedback = () => {
  // Get all feedback with pagination and sorting
  const getFeedbackList = async (options: {
    page?: number
    limit?: number
    sortBy?: 'newest' | 'oldest' | 'mostVoted'
    userId?: string
    status?: string
    category?: string
    priority?: string
  } = {}) => {
    try {
      const query = new URLSearchParams()

      if (options.page) query.append('page', options.page.toString())
      if (options.limit) query.append('limit', options.limit.toString())
      if (options.sortBy) query.append('sortBy', options.sortBy)
      if (options.userId) query.append('userId', options.userId)
      if (options.status) query.append('status', options.status)
      if (options.category) query.append('category', options.category)
      if (options.priority) query.append('priority', options.priority)

      const response: FeedbackListResponse = await $fetch(`/api/feedback?${query.toString()}`)

      return { success: true, data: response }
    } catch (error: any) {
      console.error('Error fetching feedback list:', error)
      return {
        success: false,
        error: error.data?.message || 'Failed to fetch feedback'
      }
    }
  }

  // Get single feedback by ID
  const getFeedback = async (id: number) => {
    try {
      const response: FeedbackResponse = await $fetch(`/api/feedback/${id}`)
      
      return { success: true, data: response.data }
    } catch (error: any) {
      console.error('Error fetching feedback:', error)
      return { 
        success: false, 
        error: error.data?.message || 'Failed to fetch feedback' 
      }
    }
  }

  // Create new feedback
  const submitFeedback = async (feedbackData: {
    title: string
    description: string
    category: string
    contact?: string
    userId: string
  }) => {
    try {
      const response = await $fetch('/api/feedback', {
        method: 'POST',
        body: {
          title: feedbackData.title,
          content: feedbackData.description,
          category: feedbackData.category,
          contact: feedbackData.contact,
          userId: feedbackData.userId
        }
      })

      return { success: true, data: response.data }
    } catch (error: any) {
      console.error('Error creating feedback:', error)
      return {
        success: false,
        error: error.data?.message || 'Failed to create feedback'
      }
    }
  }

  // Vote on feedback
  const voteFeedback = async (feedbackId: number, voted: boolean) => {
    try {
      const response = await $fetch(`/api/feedback/${feedbackId}/vote`, {
        method: 'POST',
        body: { voted }
      })
      
      return { success: true, data: response.data }
    } catch (error: any) {
      console.error('Error voting on feedback:', error)
      return { 
        success: false, 
        error: error.data?.message || 'Failed to vote on feedback' 
      }
    }
  }

  // Get user information
  const getUserInfo = async (userId: string) => {
    try {
      const response: UserResponse = await $fetch(`/api/users/${userId}`)

      return { success: true, data: response.data }
    } catch (error: any) {
      console.error('Error fetching user info:', error)
      return {
        success: false,
        error: error.data?.message || 'Failed to fetch user info'
      }
    }
  }

  return {
    getFeedbackList,
    getFeedback,
    submitFeedback,
    voteFeedback,
    getUserInfo
  }
}
