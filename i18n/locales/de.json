{"nav": {"home": "Startseite", "submit": "Feed<PERSON>", "login": "Anmelden", "register": "Registrieren", "logout": "Abmelden"}, "home": {"title": "Voice.new - Nutzerfeedback Sam<PERSON>n", "description": "Teilen Sie Ihr Feedback und helfen Sie uns bei der Verbesserung. Stimmen Sie für Vorschläge aus der Community ab.", "subtitle": "Community-Feedback-Plattform", "noFeedback": "<PERSON>ch kein Feedback vorhanden. <PERSON><PERSON> Erste, der seine Gedanken teilt!", "loadMore": "<PERSON><PERSON>"}, "feedback": {"title": "Feed<PERSON>", "description": "Teilen Sie Ihre Gedanken und Vorschläge mit uns", "titleLabel": "Titel", "titlePlaceholder": "Kurze Beschreibung Ihres Feedbacks", "contentLabel": "Beschreibung", "contentPlaceholder": "Detaillierte Beschreibung Ihres Feedbacks oder Vorschlags", "submit": "Feed<PERSON>", "submitting": "Wird Eingereicht...", "success": "Feedback erfolg<PERSON>ich eingereicht!", "error": "Fehler beim Einreichen des Feedbacks. Bitte versuchen Sie es erneut.", "votes": "Stimmen", "vote": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "voted": "Abgesti<PERSON><PERSON>"}, "auth": {"login": "Anmelden", "register": "Registrieren", "email": "E-Mail", "password": "Passwort", "confirmPassword": "Passwort Bestätigen", "name": "Name", "loginTitle": "<PERSON><PERSON> Ihrem <PERSON>", "registerTitle": "<PERSON>eues <PERSON>", "loginDescription": "Greifen Sie auf Ihr Konto zu, um Feedback einzureichen und abzustimmen", "registerDescription": "Treten Sie unserer Community bei, um Ihr Feedback zu teilen", "loginButton": "Anmelden", "registerButton": "<PERSON><PERSON>", "loginLink": "Haben Sie bereits ein Konto? Melden Sie sich an", "registerLink": "Haben Si<PERSON> kein Konto? Registrieren Sie sich", "loginSuccess": "Anmeldung erfolgreich!", "registerSuccess": "Konto erfolgreich erstellt!", "loginError": "Ungültige E-Mail oder Passwort", "registerError": "Fehler beim Erstellen des Kontos", "required": "<PERSON><PERSON> ist erford<PERSON>lich", "invalidEmail": "Bitte geben Si<PERSON> eine gültige E-Mail ein", "passwordMismatch": "Passwörter stimmen nicht überein", "minLength": "Mindestens {length} <PERSON><PERSON><PERSON>"}, "common": {"loading": "Wird <PERSON>...", "error": "Ein Fehler ist aufgetreten", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel": "Abbrechen", "save": "Speichern", "delete": "Löschen", "edit": "<PERSON><PERSON><PERSON>", "back": "Zurück", "next": "<PERSON><PERSON>", "previous": "<PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON>", "filter": "Filtern", "sort": "<PERSON><PERSON><PERSON><PERSON>", "newest": "Neueste", "oldest": "Älteste", "mostVoted": "<PERSON><PERSON>"}}