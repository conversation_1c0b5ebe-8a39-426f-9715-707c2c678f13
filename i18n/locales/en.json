{"nav": {"home": "Home", "submit": "Submit <PERSON>", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout"}, "home": {"title": "Voice.new - Collect User <PERSON>back", "description": "Share your feedback and help us improve. Vote on suggestions from the community.", "subtitle": "Community Feedback Platform", "noFeedback": "No feedback yet. Be the first to share your thoughts!", "loadMore": "Load More"}, "feedback": {"title": "Submit <PERSON>", "description": "Share your thoughts and suggestions with us", "titleLabel": "Title", "titlePlaceholder": "Brief description of your feedback", "contentLabel": "Description", "contentPlaceholder": "Detailed description of your feedback or suggestion", "submit": "Submit <PERSON>", "submitting": "Submitting...", "success": "<PERSON><PERSON><PERSON> submitted successfully!", "error": "Failed to submit feedback. Please try again.", "votes": "votes", "vote": "Vote", "voted": "Voted"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Name", "loginTitle": "Login to Your Account", "registerTitle": "Create New Account", "loginDescription": "Access your account to submit and vote on feedback", "registerDescription": "Join our community to share your feedback", "loginButton": "Sign In", "registerButton": "Create Account", "loginLink": "Already have an account? Sign in", "registerLink": "Don't have an account? Sign up", "loginSuccess": "Login successful!", "registerSuccess": "Account created successfully!", "loginError": "Invalid email or password", "registerError": "Failed to create account", "required": "This field is required", "invalidEmail": "Please enter a valid email", "passwordMismatch": "Passwords do not match", "minLength": "Minimum {length} characters required"}, "common": {"loading": "Loading...", "error": "An error occurred", "retry": "Retry", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "newest": "Newest", "oldest": "Oldest", "mostVoted": "Most Voted", "language": "Language"}}