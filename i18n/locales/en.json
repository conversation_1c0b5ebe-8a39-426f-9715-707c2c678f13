{"nav": {"home": "Home", "submit": "Submit <PERSON>", "login": "<PERSON><PERSON>", "register": "Register", "logout": "Logout"}, "landing": {"title": "Voice.new - Where Every Voice Matters", "description": "Beautiful feedback collection that turns user insights into product decisions.", "hero": {"title": "Where Every Voice Matters", "subtitle": "Collect, organize, and act on feedback that drives your product forward.", "getStarted": "Start Listening", "signIn": "Sign In"}, "features": {"title": "Everything You Need to Understand Your Users", "subtitle": "Simple, powerful tools to collect and organize feedback from your community.", "collect": {"title": "Easy Collection", "description": "Create a custom feedback page for your product. Users can submit suggestions with just a few clicks."}, "vote": {"title": "Community Voting", "description": "Let your users vote on feedback to help you prioritize the most requested features and improvements."}, "analyze": {"title": "Smart Analytics", "description": "Get insights into what your users really want with built-in analytics and reporting tools."}}, "howItWorks": {"title": "How It Works", "subtitle": "Get started in minutes with our simple three-step process.", "step1": {"title": "Create Your Page", "description": "Sign up and create your personalized feedback collection page in seconds."}, "step2": {"title": "Share With Users", "description": "Share your unique link with customers, users, or team members to start collecting feedback."}, "step3": {"title": "Prioritize & Act", "description": "Use community voting and analytics to understand what to build next."}}, "cta": {"title": "Ready to Start\nCollecting Feedback?", "subtitle": "Join thousands of teams already using voice.new to build better products.", "button": "Create Your Free Account"}}, "userPage": {"subtitle": "Share your feedback and suggestions", "feedbackTitle": "Community Feedback", "noFeedback": "No feedback yet", "noFeedbackDescription": "Be the first to share your thoughts and suggestions!"}, "feedback": {"title": "Submit <PERSON>", "titleFor": "Submit <PERSON><PERSON><PERSON> for {name}", "description": "Share your thoughts and suggestions with us", "hasContact": "Has contact info", "form": {"title": "Title", "titlePlaceholder": "Brief description of your feedback", "description": "Description", "descriptionPlaceholder": "Detailed description of your feedback or suggestion", "category": "Category", "selectCategory": "Select a category", "contact": "Contact Email", "contactPlaceholder": "your#email.com optional", "submit": "Submit <PERSON>", "success": "<PERSON><PERSON><PERSON> submitted successfully!"}, "categories": {"feature": "Feature Request", "bug": "Bug Report", "improvement": "Improvement", "other": "Other"}, "status": {"open": "Open", "underReview": "Under Review", "planned": "Planned", "inProgress": "In Progress", "completed": "Completed", "declined": "Declined"}, "priority": {"low": "Low", "medium": "Medium", "high": "High", "critical": "Critical"}, "votes": "votes", "vote": "Vote", "voted": "Voted", "contactInfo": "Contact Information", "voteSection": "Vote", "statusInfo": "Status & Priority", "stats": "Statistics", "created": "Created", "updated": "Updated", "additionalInfo": "Additional Info", "category": "Category", "contactAvailable": "Available"}, "auth": {"login": "<PERSON><PERSON>", "register": "Register", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "name": "Name", "loginTitle": "Login to Your Account", "registerTitle": "Create New Account", "loginDescription": "Access your account to submit and vote on feedback", "registerDescription": "Join our community to share your feedback", "loginButton": "Sign In", "registerButton": "Create Account", "loginLink": "Already have an account? Sign in", "registerLink": "Don't have an account? Sign up", "loginSuccess": "Login successful!", "registerSuccess": "Account created successfully!", "loginError": "Invalid email or password", "registerError": "Failed to create account", "required": "This field is required", "invalidEmail": "Please enter a valid email", "passwordMismatch": "Passwords do not match", "minLength": "Minimum {length} characters required"}, "common": {"loading": "Loading...", "submitting": "Submitting...", "optional": "optional", "items": "items", "error": "An error occurred", "retry": "Retry", "cancel": "Cancel", "save": "Save", "delete": "Delete", "edit": "Edit", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "sort": "Sort", "newest": "Newest", "oldest": "Oldest", "mostVoted": "Most Voted", "language": "Language", "viewDetails": "View Details"}, "pagination": {"showing": "Showing", "to": "to", "of": "of", "results": "results", "pageOf": "Page {current} of {total}"}, "comments": {"title": "Comments", "addComment": "Add a Comment", "yourComment": "Your Comment", "placeholder": "Share your thoughts about this feedback...", "name": "Name", "namePlaceholder": "Your name", "email": "Email", "emailPlaceholder": "your#email.com", "submit": "Post Comment", "noComments": "No comments yet", "beFirst": "Be the first to share your thoughts!"}, "footer": {"product": "Product", "features": "Features", "pricing": "Pricing", "demo": "Demo", "integrations": "Integrations", "resources": "Resources", "blog": "Blog", "help": "Help Center", "documentation": "Documentation", "api": "API", "legal": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service", "security": "Security", "contact": "Contact Us", "allRightsReserved": "All rights reserved.", "madeWith": "Made with", "forCommunity": "for the community"}}