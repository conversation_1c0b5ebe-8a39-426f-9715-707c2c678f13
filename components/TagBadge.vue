<template>
  <span
    class="inline-flex items-center px-1.5 py-0.5 text-xs font-medium border border-current"
    :style="tagStyle"
    :title="tag.description"
  >
    {{ tag.name }}
  </span>
</template>

<script setup>
const props = defineProps({
  tag: {
    type: Object,
    required: true
  }
})

const tagStyle = computed(() => {
  return {
    color: props.tag.color,
    borderColor: props.tag.color,
    backgroundColor: 'transparent'
  }
})
</script>
