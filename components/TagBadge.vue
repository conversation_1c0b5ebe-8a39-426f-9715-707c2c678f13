<template>
  <span 
    class="inline-flex items-center px-2 py-1 text-xs font-medium border-0"
    :style="tagStyle"
    :title="tag.description"
  >
    {{ tag.name }}
  </span>
</template>

<script setup>
const props = defineProps({
  tag: {
    type: Object,
    required: true
  }
})

// Convert hex color to RGB for background opacity
const hexToRgb = (hex) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

const tagStyle = computed(() => {
  const rgb = hexToRgb(props.tag.color)
  if (!rgb) return {}
  
  return {
    backgroundColor: `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.1)`,
    color: props.tag.color,
    border: `1px solid rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, 0.2)`
  }
})
</script>
