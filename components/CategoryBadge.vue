<template>
  <span 
    class="inline-flex items-center px-2 py-1 text-xs font-medium border-0"
    :class="categoryClass"
  >
    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path v-if="category === 'feature'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
      <path v-else-if="category === 'bug'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
      <path v-else-if="category === 'improvement'" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
      <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
    {{ categoryText }}
  </span>
</template>

<script setup>
const props = defineProps({
  category: {
    type: String,
    required: true
  }
})

const { t } = useI18n()

const categoryConfig = computed(() => {
  const configs = {
    'feature': {
      text: t('feedback.categories.feature'),
      bgClass: 'bg-blue-50 text-blue-700'
    },
    'bug': {
      text: t('feedback.categories.bug'),
      bgClass: 'bg-red-50 text-red-700'
    },
    'improvement': {
      text: t('feedback.categories.improvement'),
      bgClass: 'bg-green-50 text-green-700'
    },
    'other': {
      text: t('feedback.categories.other'),
      bgClass: 'bg-gray-50 text-gray-700'
    }
  }
  return configs[props.category] || configs['other']
})

const categoryClass = computed(() => categoryConfig.value.bgClass)
const categoryText = computed(() => categoryConfig.value.text)
</script>
