<template>
  <div v-if="totalPages > 1" class="flex items-center justify-between border-t border-color pt-6">
    <!-- Mobile pagination -->
    <div class="flex flex-1 justify-between sm:hidden">
      <button
        @click="goToPage(currentPage - 1)"
        :disabled="currentPage <= 1"
        class="btn btn-secondary"
        :class="{ 'opacity-50 cursor-not-allowed': currentPage <= 1 }"
      >
        {{ $t('common.previous') }}
      </button>
      <span class="text-sm text-secondary">
        {{ $t('pagination.pageOf', { current: currentPage, total: totalPages }) }}
      </span>
      <button
        @click="goToPage(currentPage + 1)"
        :disabled="currentPage >= totalPages"
        class="btn btn-secondary"
        :class="{ 'opacity-50 cursor-not-allowed': currentPage >= totalPages }"
      >
        {{ $t('common.next') }}
      </button>
    </div>

    <!-- Desktop pagination -->
    <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
      <div>
        <p class="text-sm text-secondary">
          {{ $t('pagination.showing') }}
          <span class="font-medium">{{ startItem }}</span>
          {{ $t('pagination.to') }}
          <span class="font-medium">{{ endItem }}</span>
          {{ $t('pagination.of') }}
          <span class="font-medium">{{ totalItems }}</span>
          {{ $t('pagination.results') }}
        </p>
      </div>
      <div>
        <nav class="isolate inline-flex space-x-1" aria-label="Pagination">
          <!-- Previous button -->
          <button
            @click="goToPage(currentPage - 1)"
            :disabled="currentPage <= 1"
            class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-secondary hover:bg-secondary transition-colors"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage <= 1 }"
          >
            <span class="sr-only">{{ $t('common.previous') }}</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd" />
            </svg>
          </button>

          <!-- Page numbers -->
          <template v-for="page in visiblePages" :key="page">
            <button
              v-if="page !== '...'"
              @click="goToPage(page)"
              class="relative inline-flex items-center px-4 py-2 text-sm font-medium transition-colors"
              :class="page === currentPage 
                ? 'bg-primary text-white' 
                : 'text-secondary hover:bg-secondary'"
            >
              {{ page }}
            </button>
            <span
              v-else
              class="relative inline-flex items-center px-4 py-2 text-sm font-medium text-secondary"
            >
              ...
            </span>
          </template>

          <!-- Next button -->
          <button
            @click="goToPage(currentPage + 1)"
            :disabled="currentPage >= totalPages"
            class="relative inline-flex items-center px-2 py-2 text-sm font-medium text-secondary hover:bg-secondary transition-colors"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage >= totalPages }"
          >
            <span class="sr-only">{{ $t('common.next') }}</span>
            <svg class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd" />
            </svg>
          </button>
        </nav>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  },
  totalItems: {
    type: Number,
    required: true
  },
  itemsPerPage: {
    type: Number,
    default: 10
  }
})

const emit = defineEmits(['page-change'])

const startItem = computed(() => {
  return (props.currentPage - 1) * props.itemsPerPage + 1
})

const endItem = computed(() => {
  return Math.min(props.currentPage * props.itemsPerPage, props.totalItems)
})

// Generate visible page numbers with ellipsis
const visiblePages = computed(() => {
  const pages = []
  const total = props.totalPages
  const current = props.currentPage
  
  if (total <= 7) {
    // Show all pages if total is 7 or less
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    // Always show first page
    pages.push(1)
    
    if (current <= 4) {
      // Show pages 2-5 and ellipsis
      for (let i = 2; i <= 5; i++) {
        pages.push(i)
      }
      pages.push('...')
    } else if (current >= total - 3) {
      // Show ellipsis and last 4 pages
      pages.push('...')
      for (let i = total - 4; i <= total - 1; i++) {
        pages.push(i)
      }
    } else {
      // Show ellipsis, current page area, and ellipsis
      pages.push('...')
      for (let i = current - 1; i <= current + 1; i++) {
        pages.push(i)
      }
      pages.push('...')
    }
    
    // Always show last page (if not already included)
    if (total > 1) {
      pages.push(total)
    }
  }
  
  return pages
})

const goToPage = (page) => {
  if (page >= 1 && page <= props.totalPages && page !== props.currentPage) {
    emit('page-change', page)
  }
}
</script>
