<template>
  <button 
    @click="handleVote"
    class="vote-btn"
    :class="{ voted: voted }"
    :disabled="isLoading"
    :title="voted ? $t('feedback.voted') : $t('feedback.vote')"
  >
    <!-- Up arrow icon -->
    <svg class="w-4 h-4 mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7" />
    </svg>
    
    <!-- Vote count -->
    <span class="text-xs font-medium">{{ votes }}</span>
  </button>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
  votes: {
    type: Number,
    default: 0
  },
  voted: {
    type: Boolean,
    default: false
  },
  feedbackId: {
    type: [String, Number],
    required: true
  }
})

const emit = defineEmits(['vote'])

const isLoading = ref(false)

const handleVote = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  
  try {
    // Emit vote event to parent
    emit('vote', {
      feedbackId: props.feedbackId,
      voted: !props.voted
    })
  } catch (error) {
    console.error('Vote error:', error)
  } finally {
    isLoading.value = false
  }
}
</script>
