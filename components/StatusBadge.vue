<template>
  <span 
    class="inline-flex items-center px-2 py-1 text-xs font-medium border-0"
    :class="statusClass"
  >
    <span class="w-1.5 h-1.5 mr-1.5 rounded-full" :class="dotClass"></span>
    {{ statusText }}
  </span>
</template>

<script setup>
const props = defineProps({
  status: {
    type: String,
    required: true
  }
})

const { t } = useI18n()

const statusConfig = computed(() => {
  const configs = {
    'open': {
      text: t('feedback.status.open'),
      bgClass: 'bg-gray-100 text-gray-800',
      dotClass: 'bg-gray-500'
    },
    'under-review': {
      text: t('feedback.status.underReview'),
      bgClass: 'bg-yellow-100 text-yellow-800',
      dotClass: 'bg-yellow-500'
    },
    'planned': {
      text: t('feedback.status.planned'),
      bgClass: 'bg-blue-100 text-blue-800',
      dotClass: 'bg-blue-500'
    },
    'in-progress': {
      text: t('feedback.status.inProgress'),
      bgClass: 'bg-purple-100 text-purple-800',
      dotClass: 'bg-purple-500'
    },
    'completed': {
      text: t('feedback.status.completed'),
      bgClass: 'bg-green-100 text-green-800',
      dotClass: 'bg-green-500'
    },
    'declined': {
      text: t('feedback.status.declined'),
      bgClass: 'bg-red-100 text-red-800',
      dotClass: 'bg-red-500'
    }
  }
  return configs[props.status] || configs['open']
})

const statusClass = computed(() => statusConfig.value.bgClass)
const dotClass = computed(() => statusConfig.value.dotClass)
const statusText = computed(() => statusConfig.value.text)
</script>
