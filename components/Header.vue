<template>
  <header class="bg-primary border-b border-color sticky top-0 z-50">
    <div class="container mx-auto px-4">
      <div class="flex items-center justify-between h-16">
        <!-- Logo -->
        <NuxtLink to="/" class="flex items-center space-x-3">
          <div class="w-8 h-8 blue-accent flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd" />
            </svg>
          </div>
          <span class="text-xl font-bold text-primary">voice.new</span>
        </NuxtLink>

        <!-- Desktop Navigation -->
        <nav class="hidden md:flex items-center space-x-1">
          <NuxtLink to="/" class="nav-link" :class="{ active: $route.path === '/' }">
            {{ $t('nav.home') }}
          </NuxtLink>
          <NuxtLink to="/submit" class="nav-link" :class="{ active: $route.path === '/submit' }">
            {{ $t('nav.submit') }}
          </NuxtLink>
        </nav>

        <!-- Right side controls -->
        <div class="flex items-center space-x-3">
          <!-- Language Selector -->
          <LanguageSelector />
          
          <!-- Theme Toggle -->
          <ThemeToggle />
          
          <!-- Auth buttons -->
          <div v-if="!isLoggedIn" class="hidden sm:flex items-center space-x-2">
            <NuxtLink to="/login" class="btn btn-ghost">
              {{ $t('nav.login') }}
            </NuxtLink>
            <NuxtLink to="/register" class="btn btn-primary">
              {{ $t('nav.register') }}
            </NuxtLink>
          </div>

          <!-- User menu -->
          <div v-else class="relative">
            <button @click="showUserMenu = !showUserMenu" class="flex items-center space-x-2 btn btn-ghost">
              <div class="w-6 h-6 blue-accent flex items-center justify-center">
                <span class="text-xs text-white font-medium">{{ user?.name?.charAt(0).toUpperCase() }}</span>
              </div>
              <span class="hidden sm:block">{{ user?.name }}</span>
            </button>
            
            <!-- User dropdown -->
            <div v-if="showUserMenu" class="absolute right-0 mt-2 w-48 bg-primary border border-color shadow-lg py-1 z-50">
              <button @click="logout" class="w-full text-left px-4 py-2 text-sm text-secondary hover:bg-secondary">
                {{ $t('nav.logout') }}
              </button>
            </div>
          </div>
          
          <!-- Mobile menu button -->
          <button @click="showMobileMenu = !showMobileMenu" class="md:hidden btn btn-ghost p-2">
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-if="showMobileMenu" class="md:hidden py-4 border-t border-color">
        <nav class="flex flex-col space-y-2">
          <NuxtLink to="/" class="nav-link" :class="{ active: $route.path === '/' }" @click="showMobileMenu = false">
            {{ $t('nav.home') }}
          </NuxtLink>
          <NuxtLink to="/submit" class="nav-link" :class="{ active: $route.path === '/submit' }" @click="showMobileMenu = false">
            {{ $t('nav.submit') }}
          </NuxtLink>
          
          <div v-if="!isLoggedIn" class="flex flex-col space-y-2 pt-2 border-t border-color">
            <NuxtLink to="/login" class="nav-link" @click="showMobileMenu = false">
              {{ $t('nav.login') }}
            </NuxtLink>
            <NuxtLink to="/register" class="nav-link" @click="showMobileMenu = false">
              {{ $t('nav.register') }}
            </NuxtLink>
          </div>

          <div v-else class="flex flex-col space-y-2 pt-2 border-t border-color">
            <div class="px-3 py-2 text-sm text-secondary">
              {{ user?.name }}
            </div>
            <button @click="logout" class="nav-link text-left">
              {{ $t('nav.logout') }}
            </button>
          </div>
        </nav>
      </div>
    </div>
  </header>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import LanguageSelector from './LanguageSelector.vue'
import ThemeToggle from './ThemeToggle.vue'

const { user, isLoggedIn, logout: authLogout } = useAuth()

const showMobileMenu = ref(false)
const showUserMenu = ref(false)

const logout = async () => {
  showUserMenu.value = false
  await authLogout()
}

// Close menus when clicking outside
const handleClickOutside = (event) => {
  if (!event.target.closest('.relative')) {
    showUserMenu.value = false
  }
  if (!event.target.closest('header')) {
    showMobileMenu.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
