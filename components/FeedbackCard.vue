<template>
  <div class="card card-interactive fade-in border-l-4" :class="statusBorderClass">
    <div class="flex gap-4">
      <!-- Vote button -->
      <VoteButton
        :votes="feedback.votes"
        :voted="feedback.userVoted"
        :feedback-id="feedback.id"
        @vote="handleVote"
      />

      <!-- Content -->
      <div class="flex-1 min-w-0">
        <!-- Header with status and priority -->
        <div class="flex items-center gap-2 mb-2">
          <h3 class="text-lg font-semibold text-primary line-clamp-2 flex-1">
            <NuxtLink :to="`/feedback/${feedback.id}`" class="hover:text-primary-hover">
              {{ feedback.title }}
            </NuxtLink>
          </h3>
          <StatusBadge :status="feedback.status" />
          <PriorityBadge :priority="feedback.priority" />
        </div>

        <!-- Tags -->
        <div v-if="feedback.tags && feedback.tags.length > 0" class="flex flex-wrap gap-1 mb-3">
          <TagBadge
            v-for="tag in feedback.tags"
            :key="tag.id"
            :tag="tag"
          />
        </div>

        <p class="text-secondary mb-4 line-clamp-3">
          {{ feedback.content }}
        </p>

        <div class="flex items-center justify-between text-sm text-tertiary">
          <div class="flex items-center space-x-4">
            <span>{{ feedback.author }}</span>
            <span>•</span>
            <span>{{ formatDate(feedback.createdAt) }}</span>
            <span>•</span>
            <CategoryBadge :category="feedback.category" />
            <span v-if="feedback.contact" class="flex items-center">
              •
              <svg class="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
              </svg>
            </span>
          </div>

          <NuxtLink
            :to="`/feedback/${feedback.id}`"
            class="text-primary hover:underline font-medium"
          >
            {{ $t('common.viewDetails') }}
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import VoteButton from './VoteButton.vue'
import StatusBadge from './StatusBadge.vue'
import PriorityBadge from './PriorityBadge.vue'
import TagBadge from './TagBadge.vue'
import CategoryBadge from './CategoryBadge.vue'

const props = defineProps({
  feedback: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['vote'])

const handleVote = (data) => {
  emit('vote', data)
}

// Status border color mapping
const statusBorderClass = computed(() => {
  const statusColors = {
    'open': 'border-l-gray-400',
    'under-review': 'border-l-yellow-500',
    'planned': 'border-l-blue-500',
    'in-progress': 'border-l-purple-500',
    'completed': 'border-l-green-500',
    'declined': 'border-l-red-500'
  }
  return statusColors[props.feedback.status] || 'border-l-gray-400'
})

const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`
  } else if (diffInHours < 24 * 7) {
    const days = Math.floor(diffInHours / 24)
    return `${days}d ago`
  } else {
    return date.toLocaleDateString()
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
