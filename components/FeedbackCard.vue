<template>
  <div class="card card-interactive fade-in">
    <div class="flex gap-4">
      <!-- Vote button -->
      <VoteButton 
        :votes="feedback.votes" 
        :voted="feedback.userVoted"
        :feedback-id="feedback.id"
        @vote="handleVote"
      />
      
      <!-- Content -->
      <div class="flex-1 min-w-0">
        <h3 class="text-lg font-semibold text-primary mb-2 line-clamp-2">
          {{ feedback.title }}
        </h3>
        
        <p class="text-secondary mb-4 line-clamp-3">
          {{ feedback.content }}
        </p>
        
        <div class="flex items-center justify-between text-sm text-tertiary">
          <div class="flex items-center space-x-4">
            <span>{{ feedback.author }}</span>
            <span>{{ formatDate(feedback.createdAt) }}</span>
          </div>
          
          <NuxtLink 
            :to="`/feedback/${feedback.id}`"
            class="text-primary hover:underline"
          >
            View Details
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import VoteButton from './VoteButton.vue'

const props = defineProps({
  feedback: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['vote'])

const handleVote = (data) => {
  emit('vote', data)
}

const formatDate = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))
  
  if (diffInHours < 1) {
    return 'Just now'
  } else if (diffInHours < 24) {
    return `${diffInHours}h ago`
  } else if (diffInHours < 24 * 7) {
    const days = Math.floor(diffInHours / 24)
    return `${days}d ago`
  } else {
    return date.toLocaleDateString()
  }
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
