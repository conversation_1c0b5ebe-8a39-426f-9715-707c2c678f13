<template>
  <div class="relative">
    <button @click="showDropdown = !showDropdown" class="btn btn-ghost p-2" :title="$t('common.language')">
      <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
      </svg>
    </button>
    
    <div v-if="showDropdown" class="absolute right-0 mt-2 w-48 bg-primary border border-color rounded-lg shadow-lg py-1 z-50">
      <button
        v-for="locale in availableLocales"
        :key="locale.code"
        @click="switchLanguage(locale.code)"
        class="w-full text-left px-4 py-2 text-sm text-secondary hover:bg-secondary flex items-center justify-between"
        :class="{ 'text-primary bg-secondary': locale.code === currentLocale }"
      >
        <span>{{ locale.name }}</span>
        <span v-if="locale.code === currentLocale" class="text-primary">✓</span>
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const { locale, locales, setLocale } = useI18n()
const switchLocalePath = useSwitchLocalePath()
const router = useRouter()

const showDropdown = ref(false)

const currentLocale = computed(() => locale.value)
const availableLocales = computed(() => locales.value)

const switchLanguage = async (newLocale) => {
  showDropdown.value = false
  
  if (newLocale !== currentLocale.value) {
    const path = switchLocalePath(newLocale)
    await router.push(path)
  }
}

// Close dropdown when clicking outside
const handleClickOutside = (event) => {
  if (!event.target.closest('.relative')) {
    showDropdown.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
