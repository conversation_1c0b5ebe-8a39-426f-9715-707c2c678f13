<template>
  <footer class="blue-accent mt-16">
    <div class="container mx-auto px-4 py-12">
      <div class="flex flex-col md:flex-row justify-between items-center">
        <div class="flex items-center space-x-3 mb-6 md:mb-0">
          <div class="w-8 h-8 bg-white bg-opacity-20 flex items-center justify-center">
            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd" />
            </svg>
          </div>
          <span class="text-xl font-bold text-white">voice.new</span>
        </div>

        <div class="text-sm text-white text-opacity-90 text-center md:text-right">
          <p class="font-medium">&copy; {{ currentYear }} voice.new. All rights reserved.</p>
          <p class="mt-2 text-white text-opacity-75">{{ $t('home.subtitle') }}</p>
        </div>
      </div>

      <!-- Additional footer content -->
      <div class="mt-8 pt-8 border-t border-white border-opacity-20">
        <div class="text-center text-white text-opacity-75 text-sm">
          <p>Built with ❤️ for the community</p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'

const currentYear = computed(() => new Date().getFullYear())
</script>
