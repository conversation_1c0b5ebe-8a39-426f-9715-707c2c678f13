<template>
  <span 
    class="inline-flex items-center px-2 py-1 text-xs font-medium border-0"
    :class="priorityClass"
  >
    <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
      <path v-if="priority === 'critical'" fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
      <path v-else-if="priority === 'high'" fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
      <path v-else-if="priority === 'medium'" fill-rule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd" />
      <path v-else fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
    </svg>
    {{ priorityText }}
  </span>
</template>

<script setup>
const props = defineProps({
  priority: {
    type: String,
    required: true
  }
})

const { t } = useI18n()

const priorityConfig = computed(() => {
  const configs = {
    'low': {
      text: t('feedback.priority.low'),
      bgClass: 'bg-gray-100 text-gray-600'
    },
    'medium': {
      text: t('feedback.priority.medium'),
      bgClass: 'bg-blue-100 text-blue-700'
    },
    'high': {
      text: t('feedback.priority.high'),
      bgClass: 'bg-orange-100 text-orange-700'
    },
    'critical': {
      text: t('feedback.priority.critical'),
      bgClass: 'bg-red-100 text-red-700'
    }
  }
  return configs[props.priority] || configs['low']
})

const priorityClass = computed(() => priorityConfig.value.bgClass)
const priorityText = computed(() => priorityConfig.value.text)
</script>
